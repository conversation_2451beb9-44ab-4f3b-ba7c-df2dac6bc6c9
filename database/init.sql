-- 图书馆管理系统数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS library CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE library;

-- 角色表
CREATE TABLE roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
    description VARCHAR(200) COMMENT '角色描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 用户表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(50) COMMENT '真实姓名',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 用户角色关联表
CREATE TABLE user_roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_role (user_id, role_id)
);

-- 图书分类表
CREATE TABLE categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description VARCHAR(500) COMMENT '分类描述',
    parent_id BIGINT COMMENT '父分类ID',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- 图书表
CREATE TABLE books (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '书名',
    author VARCHAR(100) NOT NULL COMMENT '作者',
    isbn VARCHAR(20) UNIQUE COMMENT 'ISBN',
    publisher VARCHAR(100) COMMENT '出版社',
    publish_date DATE COMMENT '出版日期',
    total_copies INT DEFAULT 1 COMMENT '总册数',
    available_copies INT DEFAULT 1 COMMENT '可借册数',
    category_id BIGINT COMMENT '分类ID',
    description TEXT COMMENT '图书描述',
    cover_url VARCHAR(500) COMMENT '封面图片URL',
    pages INT COMMENT '页数',
    language VARCHAR(20) DEFAULT 'zh-CN' COMMENT '语言',
    location VARCHAR(100) COMMENT '图书位置/书架号',
    status TINYINT DEFAULT 1 COMMENT '状态：1-可借，0-停借',
    borrow_count INT DEFAULT 0 COMMENT '借阅次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_title (title),
    INDEX idx_author (author),
    INDEX idx_category (category_id),
    INDEX idx_status (status),
    INDEX idx_isbn (isbn)
);

-- 借阅记录表
CREATE TABLE borrowing_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    book_id BIGINT NOT NULL COMMENT '图书ID',
    borrow_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '借阅日期',
    due_date TIMESTAMP NOT NULL COMMENT '应还日期',
    return_date TIMESTAMP NULL COMMENT '实际归还日期',
    renew_count INT DEFAULT 0 COMMENT '续借次数',
    status TINYINT DEFAULT 1 COMMENT '状态：1-借阅中，2-已归还，3-逾期，4-丢失',
    fine_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '罚金金额',
    fine_paid TINYINT DEFAULT 0 COMMENT '罚金是否已付：0-未付，1-已付',
    remark TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_book_id (book_id),
    INDEX idx_status (status),
    INDEX idx_due_date (due_date)
);

-- 图书馆配置表
CREATE TABLE library_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value VARCHAR(500) NOT NULL COMMENT '配置值',
    description VARCHAR(200) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 图书馆信息表
CREATE TABLE library_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '图书馆名称',
    description TEXT COMMENT '图书馆描述',
    logo_url VARCHAR(500) COMMENT '图书馆Logo',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    address TEXT COMMENT '图书馆地址',
    opening_hours VARCHAR(100) COMMENT '开放时间',
    website VARCHAR(200) COMMENT '官方网站',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 初始化基础数据
INSERT INTO roles (name, description) VALUES 
('ADMIN', '超级管理员'),
('USER', '普通用户');

INSERT INTO categories (name, description) VALUES 
('文学', '文学类图书'),
('科技', '科技类图书'),
('教育', '教育类图书'),
('生活', '生活类图书'),
('艺术', '艺术类图书');

-- 初始化图书馆配置
INSERT INTO library_config (config_key, config_value, description) VALUES
('borrow_duration_days', '30', '默认借阅天数'),
('max_borrow_books', '5', '用户最大借阅图书数量'),
('max_renew_times', '2', '最大续借次数'),
('fine_per_day', '0.50', '每日逾期罚金（元）'),
('max_fine_amount', '50.00', '最大罚金金额（元）');

INSERT INTO library_info (name, description, contact_phone, contact_email, opening_hours, website) VALUES
('智慧图书馆', '现代化数字图书馆管理系统，为读者提供便捷的图书借阅服务', '************', '<EMAIL>', '8:00-22:00', 'https://library.com');

-- 创建默认管理员账户 (密码: admin123)
INSERT INTO users (username, password, email, real_name, status) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '<EMAIL>', '系统管理员', 1);

INSERT INTO user_roles (user_id, role_id) VALUES (1, 1);
