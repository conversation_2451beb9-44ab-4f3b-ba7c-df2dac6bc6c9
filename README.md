# 图书管理系统

一个功能完整的图书管理系统，支持普通用户和超级管理员两种角色。

## 技术栈

### 后端
- SpringBoot 2.7+
- Spring Security (JWT认证)
- Spring Data JPA
- MySQL 8.0+
- Maven

### 前端
- React 18+
- TypeScript
- Ant Design
- Axios
- React Router

## 功能特性

### 普通用户功能
- 用户注册和登录
- 浏览图书目录
- 搜索图书
- 购买图书
- 查看订单历史

### 超级管理员功能
- 用户管理
- 图书管理（增删改查）
- 订单管理
- 角色权限管理
- 店铺统计
- 数据报表

## 项目结构

```
system/
├── backend/          # SpringBoot后端
│   ├── src/
│   └── pom.xml
├── frontend/         # React TypeScript前端
│   ├── src/
│   └── package.json
├── database/         # 数据库脚本
│   └── init.sql
└── README.md
```

## 快速开始

### 1. 数据库设置
```sql
-- 创建数据库
CREATE DATABASE bookstore CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. 启动后端
```bash
cd backend
mvn spring-boot:run
```

### 3. 启动前端
```bash
cd frontend
npm install
npm start
```

## API文档

后端API运行在 `http://localhost:8080`
前端应用运行在 `http://localhost:3000`

## 开发计划

- [x] 项目初始化
- [ ] 数据库设计
- [ ] 后端API开发
- [ ] 前端界面开发
- [ ] 系统集成测试
