# 图书馆管理系统

一个功能完整的图书馆管理系统，支持普通用户和超级管理员两种角色。

## 技术栈

### 后端
- SpringBoot 2.7+
- Spring Security (JWT认证)
- Spring Data JPA
- MySQL 8.0+
- Maven

### 前端
- React 18+
- TypeScript
- Ant Design
- Axios
- React Router

## 功能特性

### 普通用户功能
- 用户注册和登录
- 浏览图书目录
- 搜索图书
- 借阅图书
- 查看借阅记录
- 续借图书
- 归还图书

### 超级管理员功能
- 用户管理
- 图书管理（增删改查）
- 借阅管理
- 角色权限管理
- 图书馆统计
- 数据报表
- 罚金管理

## 项目结构

```
system/
├── backend/          # SpringBoot后端
│   ├── src/
│   └── pom.xml
├── frontend/         # React TypeScript前端
│   ├── src/
│   └── package.json
├── database/         # 数据库脚本
│   └── init.sql
└── README.md
```

## 快速开始

### 方式一：使用启动脚本（推荐）
```bash
# 确保MySQL服务正在运行
# 初始化数据库
mysql -u root -p < database/init.sql

# 运行启动脚本
./start.sh
```

### 方式二：手动启动

#### 1. 数据库设置
```sql
-- 创建数据库并初始化
mysql -u root -p < database/init.sql
```

#### 2. 启动后端
```bash
cd backend
mvn spring-boot:run
```

#### 3. 启动前端
```bash
cd frontend
npm install
npm start
```

## 访问地址

- 后端API: `http://localhost:8080/api`
- 前端应用: `http://localhost:3000`
- 测试接口: `http://localhost:8080/api/test/hello`

## 系统架构

### 数据库表结构
- `users` - 用户表
- `roles` - 角色表
- `user_roles` - 用户角色关联表
- `categories` - 图书分类表
- `books` - 图书表
- `borrowing_records` - 借阅记录表
- `library_config` - 图书馆配置表
- `library_info` - 图书馆信息表

### 后端技术栈
- SpringBoot 2.7.14
- Spring Security + JWT
- Spring Data JPA
- MySQL 8.0+
- Lombok

### 前端技术栈
- React 18 + TypeScript
- Ant Design 5.x
- React Router 6
- Axios

## 开发进度

- [x] 项目初始化和目录结构
- [x] 数据库设计和初始化脚本
- [x] SpringBoot后端基础架构
- [x] React前端基础架构
- [ ] 用户认证和权限管理
- [ ] 图书管理功能
- [ ] 借阅管理功能
- [ ] 管理员功能
- [ ] 系统集成测试
