# 图书馆管理系统

一个功能完整的图书馆管理系统，支持普通用户和超级管理员两种角色。

## 技术栈

### 后端
- SpringBoot 2.7+
- Spring Security (JWT认证)
- Spring Data JPA
- MySQL 8.0+
- Maven

### 前端
- React 18+
- TypeScript
- Ant Design
- Axios
- React Router

## 功能特性

### 普通用户功能
- 用户注册和登录
- 浏览图书目录
- 搜索图书
- 借阅图书
- 查看借阅记录
- 续借图书
- 归还图书

### 超级管理员功能
- 用户管理
- 图书管理（增删改查）
- 借阅管理
- 角色权限管理
- 图书馆统计
- 数据报表
- 罚金管理

## 项目结构

```
system/
├── backend/          # SpringBoot后端
│   ├── src/
│   └── pom.xml
├── frontend/         # React TypeScript前端
│   ├── src/
│   └── package.json
├── database/         # 数据库脚本
│   └── init.sql
└── README.md
```

## 快速开始

### 方式一：使用启动脚本（推荐）
```bash
# 确保MySQL服务正在运行
# 初始化数据库
mysql -u root -p < database/init.sql

# 运行启动脚本
./start.sh
```

### 方式二：手动启动

#### 1. 数据库设置
```sql
-- 创建数据库并初始化
mysql -u root -p < database/init.sql
```

#### 2. 启动后端
```bash
cd backend
mvn spring-boot:run
```

#### 3. 启动前端
```bash
cd frontend
npm install
npm start
```

## 访问地址

- 后端API: `http://localhost:8080/api`
- 前端应用: `http://localhost:3000`
- 测试接口: `http://localhost:8080/api/test/hello`

## 系统架构

### 数据库表结构
- `users` - 用户表
- `roles` - 角色表
- `user_roles` - 用户角色关联表
- `categories` - 图书分类表
- `books` - 图书表
- `borrowing_records` - 借阅记录表
- `library_config` - 图书馆配置表
- `library_info` - 图书馆信息表

### 后端技术栈
- SpringBoot 2.7.14
- Spring Security + JWT
- Spring Data JPA
- MySQL 8.0+
- Lombok

### 前端技术栈
- React 18 + TypeScript
- Ant Design 5.x
- React Router 6
- Axios

## 开发进度

- [x] 项目初始化和目录结构
- [x] 数据库设计和初始化脚本
- [x] SpringBoot后端基础架构
- [x] React前端基础架构
- [x] 用户认证和权限管理
- [x] 图书管理功能
- [x] 借阅管理功能
- [x] 管理员功能
- [x] 前端登录注册页面
- [x] 前端认证上下文
- [ ] 图书浏览页面
- [ ] 借阅记录页面
- [ ] 管理员后台页面
- [ ] 系统集成测试

## 已实现的功能

### 后端API (SpringBoot)
- ✅ JWT用户认证和授权
- ✅ 用户注册、登录、管理
- ✅ 图书CRUD操作
- ✅ 图书分类管理
- ✅ 借阅、归还、续借功能
- ✅ 借阅记录管理
- ✅ 管理员权限控制
- ✅ 系统统计数据
- ✅ 全局异常处理
- ✅ 数据验证

### 前端界面 (React + TypeScript)
- ✅ 用户登录注册页面
- ✅ 认证状态管理
- ✅ 响应式布局
- ✅ 路由保护
- ✅ 角色权限控制
- ✅ API服务封装

### 核心业务逻辑
- ✅ 用户角色管理（普通用户/管理员）
- ✅ 图书库存管理
- ✅ 借阅期限控制（30天）
- ✅ 续借限制（最多2次）
- ✅ 逾期罚金计算
- ✅ 借阅数量限制（最多5本）
- ✅ 图书预约功能
- ✅ 预约队列管理
- ✅ 邮件通知系统
- ✅ 逾期提醒通知
- ✅ 预约可借阅通知

## API接口文档

### 认证接口
- `POST /api/auth/signin` - 用户登录
- `POST /api/auth/signup` - 用户注册
- `POST /api/auth/signout` - 用户退出

### 图书接口
- `GET /api/books/public` - 获取公开图书列表
- `GET /api/books/public/search` - 搜索图书
- `GET /api/books/public/category/{id}` - 按分类获取图书
- `GET /api/books/public/popular` - 获取热门图书
- `GET /api/books/public/{id}` - 获取图书详情
- `GET /api/books/borrowable` - 获取可借阅图书
- `GET /api/books/admin` - 管理员获取所有图书
- `POST /api/books/admin` - 管理员创建图书
- `PUT /api/books/admin/{id}` - 管理员更新图书
- `DELETE /api/books/admin/{id}` - 管理员删除图书

### 借阅接口
- `POST /api/borrowing/borrow/{bookId}` - 借阅图书
- `POST /api/borrowing/return/{recordId}` - 归还图书
- `POST /api/borrowing/renew/{recordId}` - 续借图书
- `GET /api/borrowing/my-records` - 获取个人借阅记录
- `GET /api/borrowing/my-active` - 获取当前借阅中的图书
- `GET /api/borrowing/admin/all` - 管理员获取所有借阅记录
- `GET /api/borrowing/admin/active` - 管理员获取当前借阅记录

### 用户管理接口
- `GET /api/users/me` - 获取当前用户信息
- `GET /api/users/admin` - 管理员获取所有用户
- `GET /api/users/admin/search` - 管理员搜索用户
- `PATCH /api/users/admin/{id}/status` - 管理员更新用户状态

### 分类接口
- `GET /api/categories/public` - 获取所有分类
- `GET /api/categories/public/root` - 获取根分类
- `POST /api/categories/admin` - 管理员创建分类
- `PUT /api/categories/admin/{id}` - 管理员更新分类

### 预约接口
- `POST /api/reservations/reserve/{bookId}` - 预约图书
- `POST /api/reservations/cancel/{reservationId}` - 取消预约
- `GET /api/reservations/my-reservations` - 获取个人预约记录
- `GET /api/reservations/my-active` - 获取当前活跃预约
- `GET /api/reservations/admin/active` - 管理员获取所有活跃预约
- `POST /api/reservations/admin/cancel/{reservationId}` - 管理员取消预约

### 统计接口
- `GET /api/stats/admin/overview` - 管理员获取系统统计数据

## 新增功能详解

### 📚 图书预约功能

#### 功能特性
- **智能队列管理**: 当图书无库存时，用户可以预约，系统自动排队
- **自动通知**: 图书可借阅时，系统自动发送邮件通知
- **预约限制**: 每个用户最多可预约3本图书
- **过期处理**: 预约通知后24小时内未借阅将自动过期

#### 预约流程
1. 用户发现心仪图书无库存
2. 点击"预约"按钮加入等待队列
3. 系统显示当前队列位置
4. 图书归还后，系统通知队列中的第一位用户
5. 用户收到邮件通知，24小时内前往借阅
6. 超时未借阅，预约自动过期，通知下一位用户

### 📧 邮件通知系统

#### 通知类型
- **预约可借阅通知**: 预约的图书可以借阅时发送
- **逾期提醒**: 图书逾期时发送提醒邮件
- **归还提醒**: 图书即将到期前3天发送提醒

#### 邮件配置
1. 复制 `application-email.yml.example` 为 `application-email.yml`
2. 配置邮箱服务器信息
3. 启用邮件通知功能

#### 支持的邮箱服务
- QQ邮箱
- 163邮箱
- Gmail
- 企业邮箱

## 🚀 功能演示

### 图书预约流程演示

1. **用户登录系统**
   - 访问 http://localhost:3000
   - 使用测试账号登录或注册新账号

2. **浏览图书并预约**
   - 进入图书浏览页面
   - 找到无库存的图书
   - 点击"预约"按钮
   - 系统显示预约成功和队列位置

3. **查看预约状态**
   - 进入"我的预约"页面
   - 查看预约状态和队列位置
   - 可以取消预约

4. **管理员处理归还**
   - 管理员登录系统
   - 处理图书归还操作
   - 系统自动通知预约用户

5. **用户收到通知**
   - 用户收到邮件通知
   - 24小时内前往借阅
   - 或者预约自动过期

### 邮件通知演示

1. **配置邮件服务**
   ```bash
   # 复制配置文件
   cp backend/src/main/resources/application-email.yml.example \
      backend/src/main/resources/application-email.yml

   # 编辑配置文件，填入真实邮箱信息
   vim backend/src/main/resources/application-email.yml
   ```

2. **测试邮件发送**
   - 注册账号时使用真实邮箱
   - 预约图书并等待通知
   - 借阅图书并等待逾期提醒

## 📋 系统配置

### 预约系统配置
- 最大预约数量：3本/用户
- 预约保留时间：24小时
- 队列处理：自动化

### 邮件系统配置
- 发送频率：定时任务
- 重试机制：最多3次
- 模板支持：HTML邮件

### 定时任务
- 预约过期处理：每小时执行
- 邮件发送：每30分钟执行
- 逾期提醒：每天8点执行

## 🔧 高级功能

### 管理员功能
- 预约队列管理
- 邮件发送监控
- 系统统计报表
- 用户行为分析

### 系统监控
- 预约成功率统计
- 邮件发送成功率
- 用户活跃度分析
- 图书热度排行
