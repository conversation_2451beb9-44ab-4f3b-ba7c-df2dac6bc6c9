import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Layout, Menu, Button, Space, Typography } from 'antd';
import {
  BookOutlined,
  UserOutlined,
  HistoryOutlined,
  SettingOutlined,
  LoginOutlined,
  LogoutOutlined
} from '@ant-design/icons';
import './App.css';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

function App() {
  const [collapsed, setCollapsed] = React.useState(false);
  const [isLoggedIn, setIsLoggedIn] = React.useState(false);

  const menuItems = [
    {
      key: '1',
      icon: <BookOutlined />,
      label: '图书浏览',
    },
    {
      key: '2',
      icon: <HistoryOutlined />,
      label: '借阅记录',
    },
    {
      key: '3',
      icon: <UserOutlined />,
      label: '个人中心',
    },
    {
      key: '4',
      icon: <SettingOutlined />,
      label: '系统管理',
      children: [
        {
          key: '4-1',
          label: '用户管理',
        },
        {
          key: '4-2',
          label: '图书管理',
        },
        {
          key: '4-3',
          label: '借阅管理',
        },
      ],
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider collapsible collapsed={collapsed} onCollapse={setCollapsed}>
        <div className="logo">
          <BookOutlined style={{ marginRight: 8 }} />
          {!collapsed && '智慧图书馆'}
        </div>
        <Menu
          theme="dark"
          defaultSelectedKeys={['1']}
          mode="inline"
          items={menuItems}
        />
      </Sider>
      <Layout className="site-layout">
        <Header className="site-layout-background" style={{ padding: '0 16px', background: '#fff' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Title level={3} style={{ margin: 0 }}>图书馆管理系统</Title>
            <Space>
              {isLoggedIn ? (
                <>
                  <span>欢迎，用户</span>
                  <Button 
                    type="primary" 
                    icon={<LogoutOutlined />}
                    onClick={() => setIsLoggedIn(false)}
                  >
                    退出登录
                  </Button>
                </>
              ) : (
                <Button 
                  type="primary" 
                  icon={<LoginOutlined />}
                  onClick={() => setIsLoggedIn(true)}
                >
                  登录
                </Button>
              )}
            </Space>
          </div>
        </Header>
        <Content
          className="site-layout-background"
          style={{
            margin: '24px 16px',
            padding: 24,
            minHeight: 280,
            background: '#fff'
          }}
        >
          <Routes>
            <Route path="/" element={<div>欢迎使用图书馆管理系统</div>} />
            <Route path="/books" element={<div>图书浏览页面</div>} />
            <Route path="/borrowing" element={<div>借阅记录页面</div>} />
            <Route path="/profile" element={<div>个人中心页面</div>} />
            <Route path="/admin/*" element={<div>管理员页面</div>} />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  );
}

export default App;
