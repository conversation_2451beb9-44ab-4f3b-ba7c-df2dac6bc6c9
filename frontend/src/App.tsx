import React from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { Layout, Menu, Button, Space, Typography, Spin } from 'antd';
import {
  BookOutlined,
  UserOutlined,
  HistoryOutlined,
  SettingOutlined,
  LoginOutlined,
  LogoutOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import Login from './pages/Login';
import Register from './pages/Register';
import './App.css';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

// 受保护的路由组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return <Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }} />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

// 管理员路由组件
const AdminRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAdmin, loading } = useAuth();

  if (loading) {
    return <Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }} />;
  }

  if (!isAdmin) {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

// 主布局组件
const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = React.useState(false);
  const { user, isAuthenticated, isAdmin, logout } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const menuItems = [
    {
      key: '/books',
      icon: <BookOutlined />,
      label: '图书浏览',
    },
    ...(isAuthenticated ? [
      {
        key: '/borrowing',
        icon: <HistoryOutlined />,
        label: '借阅记录',
      },
      {
        key: '/profile',
        icon: <UserOutlined />,
        label: '个人中心',
      },
    ] : []),
    ...(isAdmin ? [
      {
        key: '/admin',
        icon: <DashboardOutlined />,
        label: '管理后台',
        children: [
          {
            key: '/admin/dashboard',
            label: '数据统计',
          },
          {
            key: '/admin/users',
            label: '用户管理',
          },
          {
            key: '/admin/books',
            label: '图书管理',
          },
          {
            key: '/admin/borrowing',
            label: '借阅管理',
          },
        ],
      },
    ] : []),
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider collapsible collapsed={collapsed} onCollapse={setCollapsed}>
        <div className="logo">
          <BookOutlined style={{ marginRight: 8 }} />
          {!collapsed && '智慧图书馆'}
        </div>
        <Menu
          theme="dark"
          defaultSelectedKeys={[window.location.pathname]}
          mode="inline"
          items={menuItems}
          onClick={({ key }) => {
            window.location.href = key;
          }}
        />
      </Sider>
      <Layout className="site-layout">
        <Header className="site-layout-background" style={{ padding: '0 16px', background: '#fff' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Title level={3} style={{ margin: 0 }}>图书馆管理系统</Title>
            <Space>
              {isAuthenticated ? (
                <>
                  <span>欢迎，{user?.username}</span>
                  {isAdmin && <span style={{ color: '#1890ff' }}>(管理员)</span>}
                  <Button
                    type="primary"
                    icon={<LogoutOutlined />}
                    onClick={handleLogout}
                  >
                    退出登录
                  </Button>
                </>
              ) : (
                <Button
                  type="primary"
                  icon={<LoginOutlined />}
                  onClick={() => window.location.href = '/login'}
                >
                  登录
                </Button>
              )}
            </Space>
          </div>
        </Header>
        <Content
          className="site-layout-background"
          style={{
            margin: '24px 16px',
            padding: 24,
            minHeight: 280,
            background: '#fff'
          }}
        >
          <Routes>
            <Route path="/" element={<div>欢迎使用图书馆管理系统</div>} />
            <Route path="/books" element={<div>图书浏览页面</div>} />
            <Route path="/borrowing" element={
              <ProtectedRoute>
                <div>借阅记录页面</div>
              </ProtectedRoute>
            } />
            <Route path="/profile" element={
              <ProtectedRoute>
                <div>个人中心页面</div>
              </ProtectedRoute>
            } />
            <Route path="/admin/*" element={
              <AdminRoute>
                <div>管理员页面</div>
              </AdminRoute>
            } />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  );
};

function App() {
  return (
    <AuthProvider>
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="/*" element={<MainLayout />} />
      </Routes>
    </AuthProvider>
  );
}

export default App;
