import api from './api';
import { PageResponse } from './bookService';

export interface BookReservation {
  id: number;
  userId: number;
  username: string;
  userRealName?: string;
  bookId: number;
  bookTitle: string;
  bookAuthor: string;
  bookIsbn?: string;
  reservationDate: string;
  expiryDate: string;
  status: number;
  statusText: string;
  queuePosition: number;
  notificationSent: number;
  remark?: string;
  createdAt: string;
  updatedAt: string;
}

class ReservationService {
  // 用户接口
  async reserveBook(bookId: number): Promise<BookReservation> {
    const response = await api.post(`/reservations/reserve/${bookId}`);
    return response.data;
  }

  async cancelReservation(reservationId: number): Promise<void> {
    await api.post(`/reservations/cancel/${reservationId}`);
  }

  async getMyReservations(page = 0, size = 10): Promise<PageResponse<BookReservation>> {
    const response = await api.get('/reservations/my-reservations', {
      params: { page, size }
    });
    return response.data;
  }

  async getMyActiveReservations(page = 0, size = 10): Promise<PageResponse<BookReservation>> {
    const response = await api.get('/reservations/my-active', {
      params: { page, size }
    });
    return response.data;
  }

  // 管理员接口
  async getAllActiveReservations(
    page = 0, 
    size = 10, 
    sortBy = 'createdAt', 
    sortDir = 'desc'
  ): Promise<PageResponse<BookReservation>> {
    const response = await api.get('/reservations/admin/active', {
      params: { page, size, sortBy, sortDir }
    });
    return response.data;
  }

  async adminCancelReservation(reservationId: number): Promise<void> {
    await api.post(`/reservations/admin/cancel/${reservationId}`);
  }

  async processReservationQueue(bookId: number): Promise<void> {
    await api.post(`/reservations/admin/process-queue/${bookId}`);
  }

  // 工具方法
  getStatusColor(status: number): string {
    switch (status) {
      case 1: return 'orange'; // 等待中
      case 2: return 'green';  // 可借阅
      case 3: return 'blue';   // 已借阅
      case 4: return 'red';    // 已过期
      case 5: return 'gray';   // 已取消
      default: return 'default';
    }
  }

  getStatusText(status: number): string {
    switch (status) {
      case 1: return '等待中';
      case 2: return '可借阅';
      case 3: return '已借阅';
      case 4: return '已过期';
      case 5: return '已取消';
      default: return '未知';
    }
  }
}

export default new ReservationService();
