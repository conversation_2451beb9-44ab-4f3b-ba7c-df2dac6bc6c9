import api from './api';

export interface Book {
  id: number;
  title: string;
  author: string;
  isbn?: string;
  publisher?: string;
  publishDate?: string;
  totalCopies: number;
  availableCopies: number;
  categoryName?: string;
  categoryId?: number;
  description?: string;
  coverUrl?: string;
  pages?: number;
  language?: string;
  location?: string;
  status: number;
  borrowCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface BookRequest {
  title: string;
  author: string;
  isbn?: string;
  publisher?: string;
  publishDate?: string;
  totalCopies: number;
  availableCopies: number;
  categoryId?: number;
  description?: string;
  coverUrl?: string;
  pages?: number;
  language?: string;
  location?: string;
}

export interface PageResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
}

class BookService {
  // 公开接口
  async getPublicBooks(page = 0, size = 10, sortBy = 'id', sortDir = 'desc'): Promise<PageResponse<Book>> {
    const response = await api.get('/books/public', {
      params: { page, size, sortBy, sortDir }
    });
    return response.data;
  }

  async searchBooks(keyword: string, page = 0, size = 10): Promise<PageResponse<Book>> {
    const response = await api.get('/books/public/search', {
      params: { keyword, page, size }
    });
    return response.data;
  }

  async getBooksByCategory(categoryId: number, page = 0, size = 10): Promise<PageResponse<Book>> {
    const response = await api.get(`/books/public/category/${categoryId}`, {
      params: { page, size }
    });
    return response.data;
  }

  async getPopularBooks(limit = 10): Promise<Book[]> {
    const response = await api.get('/books/public/popular', {
      params: { limit }
    });
    return response.data;
  }

  async getBookById(id: number): Promise<Book> {
    const response = await api.get(`/books/public/${id}`);
    return response.data;
  }

  // 认证用户接口
  async getBorrowableBooks(page = 0, size = 10): Promise<PageResponse<Book>> {
    const response = await api.get('/books/borrowable', {
      params: { page, size }
    });
    return response.data;
  }

  // 管理员接口
  async getAllBooks(page = 0, size = 10, sortBy = 'id', sortDir = 'desc'): Promise<PageResponse<Book>> {
    const response = await api.get('/books/admin', {
      params: { page, size, sortBy, sortDir }
    });
    return response.data;
  }

  async createBook(bookData: BookRequest): Promise<Book> {
    const response = await api.post('/books/admin', bookData);
    return response.data;
  }

  async updateBook(id: number, bookData: BookRequest): Promise<Book> {
    const response = await api.put(`/books/admin/${id}`, bookData);
    return response.data;
  }

  async deleteBook(id: number): Promise<void> {
    await api.delete(`/books/admin/${id}`);
  }

  async updateBookStatus(id: number, status: number): Promise<Book> {
    const response = await api.patch(`/books/admin/${id}/status`, null, {
      params: { status }
    });
    return response.data;
  }
}

export default new BookService();
