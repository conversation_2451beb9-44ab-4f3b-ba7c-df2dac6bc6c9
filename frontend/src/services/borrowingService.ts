import api from './api';
import { PageResponse } from './bookService';

export interface BorrowingRecord {
  id: number;
  userId: number;
  username: string;
  userRealName?: string;
  bookId: number;
  bookTitle: string;
  bookAuthor: string;
  bookIsbn?: string;
  borrowDate: string;
  dueDate: string;
  returnDate?: string;
  renewCount: number;
  status: number;
  statusText: string;
  fineAmount: number;
  finePaid: number;
  remark?: string;
  createdAt: string;
  updatedAt: string;
}

class BorrowingService {
  // 用户接口
  async borrowBook(bookId: number): Promise<BorrowingRecord> {
    const response = await api.post(`/borrowing/borrow/${bookId}`);
    return response.data;
  }

  async returnBook(recordId: number): Promise<BorrowingRecord> {
    const response = await api.post(`/borrowing/return/${recordId}`);
    return response.data;
  }

  async renewBook(recordId: number): Promise<BorrowingRecord> {
    const response = await api.post(`/borrowing/renew/${recordId}`);
    return response.data;
  }

  async getMyBorrowingRecords(page = 0, size = 10): Promise<PageResponse<BorrowingRecord>> {
    const response = await api.get('/borrowing/my-records', {
      params: { page, size }
    });
    return response.data;
  }

  async getMyActiveBorrowings(page = 0, size = 10): Promise<PageResponse<BorrowingRecord>> {
    const response = await api.get('/borrowing/my-active', {
      params: { page, size }
    });
    return response.data;
  }

  // 管理员接口
  async getAllBorrowingRecords(
    page = 0, 
    size = 10, 
    sortBy = 'createdAt', 
    sortDir = 'desc'
  ): Promise<PageResponse<BorrowingRecord>> {
    const response = await api.get('/borrowing/admin/all', {
      params: { page, size, sortBy, sortDir }
    });
    return response.data;
  }

  async getActiveBorrowings(page = 0, size = 10): Promise<PageResponse<BorrowingRecord>> {
    const response = await api.get('/borrowing/admin/active', {
      params: { page, size }
    });
    return response.data;
  }

  async forceReturnBook(recordId: number): Promise<BorrowingRecord> {
    const response = await api.post(`/borrowing/admin/force-return/${recordId}`);
    return response.data;
  }
}

export default new BorrowingService();
