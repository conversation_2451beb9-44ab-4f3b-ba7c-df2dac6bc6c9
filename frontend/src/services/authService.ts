import api from './api';

export interface LoginRequest {
  username: string;
  password: string;
}

export interface SignupRequest {
  username: string;
  email: string;
  password: string;
  realName?: string;
  phone?: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  realName?: string;
  phone?: string;
  roles: string[];
}

export interface LoginResponse {
  token: string;
  type: string;
  id: number;
  username: string;
  email: string;
  roles: string[];
}

class AuthService {
  async login(loginData: LoginRequest): Promise<LoginResponse> {
    const response = await api.post('/auth/signin', loginData);
    if (response.success && response.data) {
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify({
        id: response.data.id,
        username: response.data.username,
        email: response.data.email,
        roles: response.data.roles,
      }));
    }
    return response.data;
  }

  async register(signupData: SignupRequest): Promise<any> {
    const response = await api.post('/auth/signup', signupData);
    return response;
  }

  async logout(): Promise<void> {
    try {
      await api.post('/auth/signout');
    } finally {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
    }
  }

  getCurrentUser(): User | null {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch (e) {
        localStorage.removeItem('user');
      }
    }
    return null;
  }

  getToken(): string | null {
    return localStorage.getItem('token');
  }

  isAuthenticated(): boolean {
    return !!this.getToken();
  }

  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    return user?.roles?.includes(`ROLE_${role}`) || false;
  }

  isAdmin(): boolean {
    return this.hasRole('ADMIN');
  }
}

export default new AuthService();
