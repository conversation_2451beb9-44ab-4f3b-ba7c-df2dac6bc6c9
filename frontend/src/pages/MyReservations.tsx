import React, { useState, useEffect } from 'react';
import { Table, Button, message, Card, Tag, Space, Popconfirm, Tabs } from 'antd';
import { BookOutlined, ClockCircleOutlined, CheckCircleOutlined } from '@ant-design/icons';
import reservationService, { BookReservation } from '../services/reservationService';
import { PageResponse } from '../services/bookService';

const { TabPane } = Tabs;

const MyReservations: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [allReservations, setAllReservations] = useState<PageResponse<BookReservation> | null>(null);
  const [activeReservations, setActiveReservations] = useState<PageResponse<BookReservation> | null>(null);
  const [activeTab, setActiveTab] = useState('active');

  useEffect(() => {
    loadReservations();
  }, [activeTab]);

  const loadReservations = async () => {
    setLoading(true);
    try {
      if (activeTab === 'active') {
        const data = await reservationService.getMyActiveReservations();
        setActiveReservations(data);
      } else {
        const data = await reservationService.getMyReservations();
        setAllReservations(data);
      }
    } catch (error: any) {
      message.error('加载预约记录失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelReservation = async (reservationId: number) => {
    try {
      await reservationService.cancelReservation(reservationId);
      message.success('预约已取消');
      loadReservations();
    } catch (error: any) {
      message.error(error.message || '取消预约失败');
    }
  };

  const columns = [
    {
      title: '图书信息',
      key: 'book',
      render: (record: BookReservation) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
            <BookOutlined style={{ marginRight: 8 }} />
            {record.bookTitle}
          </div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            作者：{record.bookAuthor}
            {record.bookIsbn && ` | ISBN：${record.bookIsbn}`}
          </div>
        </div>
      ),
    },
    {
      title: '预约时间',
      dataIndex: 'reservationDate',
      key: 'reservationDate',
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '状态',
      key: 'status',
      render: (record: BookReservation) => (
        <Space direction="vertical" size="small">
          <Tag color={reservationService.getStatusColor(record.status)}>
            {reservationService.getStatusText(record.status)}
          </Tag>
          {record.status === 1 && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              队列位置：第 {record.queuePosition} 位
            </div>
          )}
          {record.status === 2 && (
            <div style={{ fontSize: '12px', color: '#f5222d' }}>
              <ClockCircleOutlined style={{ marginRight: 4 }} />
              请在 {new Date(record.expiryDate).toLocaleString()} 前借阅
            </div>
          )}
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (record: BookReservation) => (
        <Space>
          {(record.status === 1 || record.status === 2) && (
            <Popconfirm
              title="确定要取消这个预约吗？"
              onConfirm={() => handleCancelReservation(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger size="small">
                取消预约
              </Button>
            </Popconfirm>
          )}
          {record.status === 2 && (
            <Button type="primary" size="small">
              前往借阅
            </Button>
          )}
        </Space>
      ),
    },
  ];

  const currentData = activeTab === 'active' ? activeReservations : allReservations;

  return (
    <Card title="我的预约" style={{ margin: '24px' }}>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane 
          tab={
            <span>
              <ClockCircleOutlined />
              活跃预约
            </span>
          } 
          key="active"
        >
          <Table
            columns={columns}
            dataSource={currentData?.content || []}
            loading={loading}
            rowKey="id"
            pagination={{
              current: (currentData?.number || 0) + 1,
              pageSize: currentData?.size || 10,
              total: currentData?.totalElements || 0,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
            }}
          />
        </TabPane>
        <TabPane 
          tab={
            <span>
              <CheckCircleOutlined />
              全部预约
            </span>
          } 
          key="all"
        >
          <Table
            columns={columns}
            dataSource={currentData?.content || []}
            loading={loading}
            rowKey="id"
            pagination={{
              current: (currentData?.number || 0) + 1,
              pageSize: currentData?.size || 10,
              total: currentData?.totalElements || 0,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
            }}
          />
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default MyReservations;
