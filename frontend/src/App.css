.App {
  text-align: center;
}

.site-layout-background {
  background: #fff;
}

.logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.ant-layout-sider-collapsed .logo {
  justify-content: center;
}

.site-layout .ant-layout-header {
  padding: 0;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

.site-layout-content {
  margin: 24px 16px;
  padding: 24px;
  background: #fff;
  min-height: 360px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    height: 100vh;
    left: 0;
    z-index: 999;
  }
  
  .ant-layout-sider-collapsed {
    left: -80px;
  }
  
  .site-layout {
    margin-left: 0 !important;
  }
}
