<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图书归还提醒</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background: #f9f9f9;
            padding: 30px;
            border-radius: 0 0 8px 8px;
        }
        .book-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #feca57;
        }
        .highlight {
            color: #ff6348;
            font-weight: bold;
        }
        .info {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📅 图书归还提醒</h1>
        <p th:text="${systemName}">智慧图书馆</p>
    </div>
    
    <div class="content">
        <p>尊敬的 <span class="highlight" th:text="${userName}">用户</span>，您好！</p>
        
        <p>您借阅的图书即将到期，请及时归还：</p>
        
        <div class="book-info">
            <h3>📖 <span th:text="${bookTitle}">图书标题</span></h3>
            <p><strong>应还日期：</strong><span class="highlight" th:text="${#temporals.format(dueDate, 'yyyy-MM-dd HH:mm')}">2024-01-01 23:59</span></p>
            <p><strong>剩余天数：</strong><span th:text="${reminderDays}">3</span> 天</p>
        </div>
        
        <div class="info">
            <strong>💡 温馨提示：</strong>
            <ul>
                <li>请在到期日前归还图书，避免产生逾期罚金</li>
                <li>如需继续阅读，可以办理续借手续（最多续借2次）</li>
                <li>逾期归还将影响您的信用记录</li>
            </ul>
        </div>
        
        <p>归还地点：图书馆服务台</p>
        <p>开放时间：8:00-22:00</p>
        <p>续借方式：登录系统或前往服务台</p>
        
        <p>感谢您的配合！</p>
    </div>
    
    <div class="footer">
        <p th:text="${systemName}">智慧图书馆</p>
        <p>此邮件由系统自动发送，请勿回复</p>
    </div>
</body>
</html>
