/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/dto/UserDTO.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/repository/EmailNotificationRepository.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/service/UserService.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/config/WebSecurityConfig.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/repository/BorrowingRecordRepository.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/entity/BorrowingRecord.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/config/SchedulingConfig.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/entity/BookReservation.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/service/ScheduledTaskService.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/dto/BookDTO.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/entity/User.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/util/JwtUtils.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/controller/AuthController.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/security/AuthTokenFilter.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/service/CategoryService.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/service/ReservationService.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/dto/ApiResponse.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/dto/BookReservationDTO.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/config/JpaConfig.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/entity/Category.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/controller/ReservationController.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/entity/Role.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/service/BorrowingService.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/repository/UserRepository.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/controller/TestController.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/repository/RoleRepository.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/security/AuthEntryPointJwt.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/entity/BaseEntity.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/dto/LoginRequest.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/service/BookService.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/controller/UserController.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/exception/GlobalExceptionHandler.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/dto/BookRequest.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/entity/Book.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/security/UserDetailsImpl.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/dto/BorrowingRecordDTO.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/controller/StatsController.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/dto/LibraryStatsDTO.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/dto/JwtResponse.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/repository/CategoryRepository.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/service/StatsService.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/controller/CategoryController.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/LibraryApplication.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/dto/SignupRequest.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/security/UserDetailsServiceImpl.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/repository/BookRepository.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/repository/BookReservationRepository.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/entity/EmailNotification.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/service/EmailService.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/controller/BookController.java
/Users/<USER>/Desktop/code/system/backend/src/main/java/com/library/controller/BorrowingController.java
