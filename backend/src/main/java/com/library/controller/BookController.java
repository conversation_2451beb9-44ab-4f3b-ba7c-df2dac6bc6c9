package com.library.controller;

import com.library.dto.ApiResponse;
import com.library.dto.BookDTO;
import com.library.dto.BookRequest;
import com.library.entity.Book;
import com.library.service.BookService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/books")
@RequiredArgsConstructor
@CrossOrigin(origins = "*", maxAge = 3600)
public class BookController {
    
    private final BookService bookService;
    
    // 公开接口 - 浏览图书
    @GetMapping("/public")
    public ResponseEntity<ApiResponse<Page<BookDTO>>> getPublicBooks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<BookDTO> books = bookService.findAvailableBooks(pageable);
        return ResponseEntity.ok(ApiResponse.success(books));
    }
    
    // 公开接口 - 搜索图书
    @GetMapping("/public/search")
    public ResponseEntity<ApiResponse<Page<BookDTO>>> searchPublicBooks(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<BookDTO> books = bookService.searchBooks(keyword, pageable);
        return ResponseEntity.ok(ApiResponse.success(books));
    }
    
    // 公开接口 - 按分类查询
    @GetMapping("/public/category/{categoryId}")
    public ResponseEntity<ApiResponse<Page<BookDTO>>> getBooksByCategory(
            @PathVariable Long categoryId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<BookDTO> books = bookService.findBooksByCategory(categoryId, pageable);
        return ResponseEntity.ok(ApiResponse.success(books));
    }
    
    // 公开接口 - 热门图书
    @GetMapping("/public/popular")
    public ResponseEntity<ApiResponse<List<BookDTO>>> getPopularBooks(
            @RequestParam(defaultValue = "10") int limit) {
        
        Pageable pageable = PageRequest.of(0, limit);
        List<BookDTO> books = bookService.findPopularBooks(pageable);
        return ResponseEntity.ok(ApiResponse.success(books));
    }
    
    // 公开接口 - 图书详情
    @GetMapping("/public/{id}")
    public ResponseEntity<ApiResponse<BookDTO>> getBookById(@PathVariable Long id) {
        return bookService.findById(id)
                .map(book -> ResponseEntity.ok(ApiResponse.success(bookService.convertToDTO(book))))
                .orElse(ResponseEntity.notFound().build());
    }
    
    // 认证用户接口 - 可借阅图书
    @GetMapping("/borrowable")
    public ResponseEntity<ApiResponse<Page<BookDTO>>> getBorrowableBooks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<BookDTO> books = bookService.findBorrowableBooks(pageable);
        return ResponseEntity.ok(ApiResponse.success(books));
    }
    
    // 管理员接口 - 获取所有图书
    @GetMapping("/admin")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<BookDTO>>> getAllBooks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<BookDTO> books = bookService.findAllBooks(pageable);
        return ResponseEntity.ok(ApiResponse.success(books));
    }
    
    // 管理员接口 - 创建图书
    @PostMapping("/admin")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<BookDTO>> createBook(@Valid @RequestBody BookRequest bookRequest) {
        try {
            Book book = bookService.createBook(bookRequest);
            return ResponseEntity.ok(ApiResponse.success("图书创建成功", bookService.convertToDTO(book)));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    // 管理员接口 - 更新图书
    @PutMapping("/admin/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<BookDTO>> updateBook(
            @PathVariable Long id, 
            @Valid @RequestBody BookRequest bookRequest) {
        try {
            Book book = bookService.updateBook(id, bookRequest);
            return ResponseEntity.ok(ApiResponse.success("图书更新成功", bookService.convertToDTO(book)));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    // 管理员接口 - 删除图书
    @DeleteMapping("/admin/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> deleteBook(@PathVariable Long id) {
        try {
            bookService.deleteBook(id);
            return ResponseEntity.ok(ApiResponse.success("图书删除成功"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    // 管理员接口 - 更新图书状态
    @PatchMapping("/admin/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<BookDTO>> updateBookStatus(
            @PathVariable Long id, 
            @RequestParam Integer status) {
        try {
            Book book = bookService.updateBookStatus(id, status);
            return ResponseEntity.ok(ApiResponse.success("状态更新成功", bookService.convertToDTO(book)));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
}
