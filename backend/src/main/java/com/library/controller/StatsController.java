package com.library.controller;

import com.library.dto.ApiResponse;
import com.library.dto.LibraryStatsDTO;
import com.library.service.StatsService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/stats")
@RequiredArgsConstructor
@CrossOrigin(origins = "*", maxAge = 3600)
public class StatsController {
    
    private final StatsService statsService;
    
    @GetMapping("/admin/overview")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<LibraryStatsDTO>> getLibraryStats() {
        LibraryStatsDTO stats = statsService.getLibraryStats();
        return ResponseEntity.ok(ApiResponse.success(stats));
    }
}
