package com.library.controller;

import com.library.dto.ApiResponse;
import com.library.dto.BorrowingRecordDTO;
import com.library.entity.BorrowingRecord;
import com.library.security.UserDetailsImpl;
import com.library.service.BorrowingService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/borrowing")
@RequiredArgsConstructor
@CrossOrigin(origins = "*", maxAge = 3600)
public class BorrowingController {
    
    private final BorrowingService borrowingService;
    
    // 用户借阅图书
    @PostMapping("/borrow/{bookId}")
    public ResponseEntity<ApiResponse<BorrowingRecordDTO>> borrowBook(
            @PathVariable Long bookId,
            Authentication authentication) {
        try {
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            BorrowingRecord record = borrowingService.borrowBook(userDetails.getId(), bookId);
            return ResponseEntity.ok(ApiResponse.success("借阅成功", 
                    borrowingService.convertToDTO(record)));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    // 用户归还图书
    @PostMapping("/return/{recordId}")
    public ResponseEntity<ApiResponse<BorrowingRecordDTO>> returnBook(
            @PathVariable Long recordId,
            Authentication authentication) {
        try {
            BorrowingRecord record = borrowingService.returnBook(recordId);
            return ResponseEntity.ok(ApiResponse.success("归还成功", 
                    borrowingService.convertToDTO(record)));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    // 用户续借图书
    @PostMapping("/renew/{recordId}")
    public ResponseEntity<ApiResponse<BorrowingRecordDTO>> renewBook(
            @PathVariable Long recordId,
            Authentication authentication) {
        try {
            BorrowingRecord record = borrowingService.renewBook(recordId);
            return ResponseEntity.ok(ApiResponse.success("续借成功", 
                    borrowingService.convertToDTO(record)));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    // 用户查看自己的借阅记录
    @GetMapping("/my-records")
    public ResponseEntity<ApiResponse<Page<BorrowingRecordDTO>>> getMyBorrowingRecords(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {
        
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        Sort sort = Sort.by("createdAt").descending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<BorrowingRecordDTO> records = borrowingService
                .findUserBorrowingRecords(userDetails.getId(), pageable);
        return ResponseEntity.ok(ApiResponse.success(records));
    }
    
    // 用户查看当前借阅中的图书
    @GetMapping("/my-active")
    public ResponseEntity<ApiResponse<Page<BorrowingRecordDTO>>> getMyActiveBorrowings(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {
        
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        Sort sort = Sort.by("dueDate").ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<BorrowingRecordDTO> records = borrowingService
                .findUserActiveBorrowings(userDetails.getId(), pageable);
        return ResponseEntity.ok(ApiResponse.success(records));
    }
    
    // 管理员查看所有借阅记录
    @GetMapping("/admin/all")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<BorrowingRecordDTO>>> getAllBorrowingRecords(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<BorrowingRecordDTO> records = borrowingService.findAllBorrowingRecords(pageable);
        return ResponseEntity.ok(ApiResponse.success(records));
    }
    
    // 管理员查看当前借阅中的记录
    @GetMapping("/admin/active")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<BorrowingRecordDTO>>> getActiveBorrowings(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Sort sort = Sort.by("dueDate").ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<BorrowingRecordDTO> records = borrowingService.findActiveBorrowings(pageable);
        return ResponseEntity.ok(ApiResponse.success(records));
    }
    
    // 管理员强制归还图书
    @PostMapping("/admin/force-return/{recordId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<BorrowingRecordDTO>> forceReturnBook(@PathVariable Long recordId) {
        try {
            BorrowingRecord record = borrowingService.returnBook(recordId);
            return ResponseEntity.ok(ApiResponse.success("强制归还成功", 
                    borrowingService.convertToDTO(record)));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
}
