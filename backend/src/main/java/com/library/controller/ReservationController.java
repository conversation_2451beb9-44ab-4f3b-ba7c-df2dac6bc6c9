package com.library.controller;

import com.library.dto.ApiResponse;
import com.library.dto.BookReservationDTO;
import com.library.entity.BookReservation;
import com.library.security.UserDetailsImpl;
import com.library.service.ReservationService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/reservations")
@RequiredArgsConstructor
@CrossOrigin(origins = "*", maxAge = 3600)
public class ReservationController {
    
    private final ReservationService reservationService;
    
    // 用户预约图书
    @PostMapping("/reserve/{bookId}")
    public ResponseEntity<ApiResponse<BookReservationDTO>> reserveBook(
            @PathVariable Long bookId,
            Authentication authentication) {
        try {
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            BookReservation reservation = reservationService.reserveBook(userDetails.getId(), bookId);
            return ResponseEntity.ok(ApiResponse.success("预约成功", 
                    reservationService.convertToDTO(reservation)));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    // 用户取消预约
    @PostMapping("/cancel/{reservationId}")
    public ResponseEntity<ApiResponse<String>> cancelReservation(
            @PathVariable Long reservationId,
            Authentication authentication) {
        try {
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            reservationService.cancelReservation(reservationId, userDetails.getId());
            return ResponseEntity.ok(ApiResponse.success("预约已取消"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    // 用户查看自己的预约记录
    @GetMapping("/my-reservations")
    public ResponseEntity<ApiResponse<Page<BookReservationDTO>>> getMyReservations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {
        
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        Sort sort = Sort.by("createdAt").descending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<BookReservationDTO> reservations = reservationService
                .getUserReservations(userDetails.getId(), pageable);
        return ResponseEntity.ok(ApiResponse.success(reservations));
    }
    
    // 用户查看当前活跃预约
    @GetMapping("/my-active")
    public ResponseEntity<ApiResponse<Page<BookReservationDTO>>> getMyActiveReservations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {
        
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        Sort sort = Sort.by("queuePosition").ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<BookReservationDTO> reservations = reservationService
                .getUserActiveReservations(userDetails.getId(), pageable);
        return ResponseEntity.ok(ApiResponse.success(reservations));
    }
    
    // 管理员查看所有活跃预约
    @GetMapping("/admin/active")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<BookReservationDTO>>> getAllActiveReservations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<BookReservationDTO> reservations = reservationService.getAllActiveReservations(pageable);
        return ResponseEntity.ok(ApiResponse.success(reservations));
    }
    
    // 管理员取消预约
    @PostMapping("/admin/cancel/{reservationId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> adminCancelReservation(@PathVariable Long reservationId) {
        try {
            reservationService.adminCancelReservation(reservationId);
            return ResponseEntity.ok(ApiResponse.success("预约已取消"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    // 管理员手动处理预约队列
    @PostMapping("/admin/process-queue/{bookId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> processReservationQueue(@PathVariable Long bookId) {
        try {
            reservationService.processReservationQueueAfterReturn(bookId);
            return ResponseEntity.ok(ApiResponse.success("预约队列处理完成"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
}
