package com.library.controller;

import com.library.dto.ApiResponse;
import com.library.entity.Category;
import com.library.service.CategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/categories")
@RequiredArgsConstructor
@CrossOrigin(origins = "*", maxAge = 3600)
public class CategoryController {
    
    private final CategoryService categoryService;
    
    // 公开接口 - 获取所有活跃分类
    @GetMapping("/public")
    public ResponseEntity<ApiResponse<List<Category>>> getActiveCategories() {
        List<Category> categories = categoryService.findAllActiveCategories();
        return ResponseEntity.ok(ApiResponse.success(categories));
    }
    
    // 公开接口 - 获取根分类
    @GetMapping("/public/root")
    public ResponseEntity<ApiResponse<List<Category>>> getRootCategories() {
        List<Category> categories = categoryService.findRootCategories();
        return ResponseEntity.ok(ApiResponse.success(categories));
    }
    
    // 公开接口 - 获取子分类
    @GetMapping("/public/{parentId}/children")
    public ResponseEntity<ApiResponse<List<Category>>> getChildCategories(@PathVariable Long parentId) {
        List<Category> categories = categoryService.findByParentId(parentId);
        return ResponseEntity.ok(ApiResponse.success(categories));
    }
    
    // 管理员接口 - 创建分类
    @PostMapping("/admin")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Category>> createCategory(
            @RequestParam String name,
            @RequestParam(required = false) String description,
            @RequestParam(required = false) Long parentId,
            @RequestParam(required = false) Integer sortOrder) {
        try {
            Category category = categoryService.createCategory(name, description, parentId, sortOrder);
            return ResponseEntity.ok(ApiResponse.success("分类创建成功", category));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    // 管理员接口 - 更新分类
    @PutMapping("/admin/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Category>> updateCategory(
            @PathVariable Long id,
            @RequestParam String name,
            @RequestParam(required = false) String description,
            @RequestParam(required = false) Integer sortOrder) {
        try {
            Category category = categoryService.updateCategory(id, name, description, sortOrder);
            return ResponseEntity.ok(ApiResponse.success("分类更新成功", category));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    // 管理员接口 - 删除分类
    @DeleteMapping("/admin/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> deleteCategory(@PathVariable Long id) {
        try {
            categoryService.deleteCategory(id);
            return ResponseEntity.ok(ApiResponse.success("分类删除成功"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
}
