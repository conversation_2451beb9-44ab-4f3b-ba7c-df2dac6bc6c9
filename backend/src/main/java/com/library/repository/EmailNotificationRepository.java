package com.library.repository;

import com.library.entity.EmailNotification;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface EmailNotificationRepository extends JpaRepository<EmailNotification, Long> {
    
    @Query("SELECT en FROM EmailNotification en WHERE en.status = 0 AND en.retryCount < 3")
    List<EmailNotification> findPendingNotifications();
    
    @Query("SELECT en FROM EmailNotification en WHERE en.user.id = :userId")
    Page<EmailNotification> findByUserId(@Param("userId") Long userId, Pageable pageable);
    
    @Query("SELECT en FROM EmailNotification en WHERE en.status = :status")
    Page<EmailNotification> findByStatus(@Param("status") Integer status, Pageable pageable);
    
    @Query("SELECT en FROM EmailNotification en WHERE en.type = :type AND en.sentAt BETWEEN :startDate AND :endDate")
    List<EmailNotification> findByTypeAndDateRange(@Param("type") String type, 
                                                  @Param("startDate") LocalDateTime startDate, 
                                                  @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT COUNT(en) FROM EmailNotification en WHERE en.status = 1")
    long countSentNotifications();
    
    @Query("SELECT COUNT(en) FROM EmailNotification en WHERE en.status = 2")
    long countFailedNotifications();
}
