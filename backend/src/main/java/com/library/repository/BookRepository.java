package com.library.repository;

import com.library.entity.Book;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface BookRepository extends JpaRepository<Book, Long> {
    
    Optional<Book> findByIsbn(String isbn);
    
    boolean existsByIsbn(String isbn);
    
    @Query("SELECT b FROM Book b WHERE b.status = 1")
    Page<Book> findAvailableBooks(Pageable pageable);
    
    @Query("SELECT b FROM Book b WHERE b.category.id = :categoryId AND b.status = 1")
    Page<Book> findByCategoryId(@Param("categoryId") Long categoryId, Pageable pageable);
    
    @Query("SELECT b FROM Book b WHERE " +
           "(b.title LIKE %:keyword% OR b.author LIKE %:keyword% OR b.isbn LIKE %:keyword%) " +
           "AND b.status = 1")
    Page<Book> searchBooks(@Param("keyword") String keyword, Pageable pageable);
    
    @Query("SELECT b FROM Book b WHERE b.availableCopies > 0 AND b.status = 1")
    Page<Book> findBorrowableBooks(Pageable pageable);
    
    @Query("SELECT b FROM Book b ORDER BY b.borrowCount DESC")
    List<Book> findPopularBooks(Pageable pageable);
    
    @Query("SELECT COUNT(b) FROM Book b WHERE b.status = 1")
    long countAvailableBooks();
    
    @Query("SELECT SUM(b.totalCopies) FROM Book b WHERE b.status = 1")
    long countTotalCopies();
    
    @Query("SELECT SUM(b.availableCopies) FROM Book b WHERE b.status = 1")
    long countAvailableCopies();
}
