package com.library.repository;

import com.library.entity.BookReservation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface BookReservationRepository extends JpaRepository<BookReservation, Long> {
    
    @Query("SELECT br FROM BookReservation br WHERE br.user.id = :userId AND br.status IN (1, 2)")
    Page<BookReservation> findActiveReservationsByUserId(@Param("userId") Long userId, Pageable pageable);
    
    @Query("SELECT br FROM BookReservation br WHERE br.user.id = :userId")
    Page<BookReservation> findByUserId(@Param("userId") Long userId, Pageable pageable);
    
    @Query("SELECT br FROM BookReservation br WHERE br.book.id = :bookId AND br.status = 1 ORDER BY br.queuePosition")
    List<BookReservation> findWaitingReservationsByBookId(@Param("bookId") Long bookId);
    
    @Query("SELECT br FROM BookReservation br WHERE br.book.id = :bookId AND br.user.id = :userId AND br.status IN (1, 2)")
    Optional<BookReservation> findActiveReservationByUserAndBook(@Param("userId") Long userId, @Param("bookId") Long bookId);
    
    @Query("SELECT COUNT(br) FROM BookReservation br WHERE br.user.id = :userId AND br.status IN (1, 2)")
    long countActiveReservationsByUserId(@Param("userId") Long userId);
    
    @Query("SELECT br FROM BookReservation br WHERE br.status = 2 AND br.expiryDate < :now")
    List<BookReservation> findExpiredAvailableReservations(@Param("now") LocalDateTime now);
    
    @Query("SELECT br FROM BookReservation br WHERE br.status = 2 AND br.notificationSent = 0")
    List<BookReservation> findAvailableReservationsNotNotified();
    
    @Query("SELECT MAX(br.queuePosition) FROM BookReservation br WHERE br.book.id = :bookId AND br.status = 1")
    Integer findMaxQueuePositionByBookId(@Param("bookId") Long bookId);
    
    @Modifying
    @Query("UPDATE BookReservation br SET br.queuePosition = br.queuePosition - 1 WHERE br.book.id = :bookId AND br.queuePosition > :position AND br.status = 1")
    void updateQueuePositionsAfterCancellation(@Param("bookId") Long bookId, @Param("position") Integer position);
    
    @Query("SELECT br FROM BookReservation br WHERE br.status IN (1, 2)")
    Page<BookReservation> findActiveReservations(Pageable pageable);
    
    @Query("SELECT COUNT(br) FROM BookReservation br WHERE br.status = 1")
    long countWaitingReservations();
    
    @Query("SELECT COUNT(br) FROM BookReservation br WHERE br.status = 2")
    long countAvailableReservations();
}
