package com.library.repository;

import com.library.entity.Category;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CategoryRepository extends JpaRepository<Category, Long> {
    
    @Query("SELECT c FROM Category c WHERE c.parent IS NULL AND c.status = 1 ORDER BY c.sortOrder")
    List<Category> findRootCategories();
    
    @Query("SELECT c FROM Category c WHERE c.parent.id = :parentId AND c.status = 1 ORDER BY c.sortOrder")
    List<Category> findByParentId(Long parentId);
    
    @Query("SELECT c FROM Category c WHERE c.status = 1 ORDER BY c.sortOrder")
    List<Category> findActiveCategories();
    
    boolean existsByName(String name);
}
