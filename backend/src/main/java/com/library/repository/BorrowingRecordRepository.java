package com.library.repository;

import com.library.entity.BorrowingRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface BorrowingRecordRepository extends JpaRepository<BorrowingRecord, Long> {
    
    @Query("SELECT br FROM BorrowingRecord br WHERE br.user.id = :userId")
    Page<BorrowingRecord> findByUserId(@Param("userId") Long userId, Pageable pageable);
    
    @Query("SELECT br FROM BorrowingRecord br WHERE br.user.id = :userId AND br.status = :status")
    Page<BorrowingRecord> findByUserIdAndStatus(@Param("userId") Long userId, 
                                               @Param("status") Integer status, 
                                               Pageable pageable);
    
    @Query("SELECT br FROM BorrowingRecord br WHERE br.book.id = :bookId")
    Page<BorrowingRecord> findByBookId(@Param("bookId") Long bookId, Pageable pageable);
    
    @Query("SELECT br FROM BorrowingRecord br WHERE br.status = 1") // 借阅中
    Page<BorrowingRecord> findActiveBorrowings(Pageable pageable);
    
    @Query("SELECT br FROM BorrowingRecord br WHERE br.dueDate < :now AND br.status = 1")
    List<BorrowingRecord> findOverdueRecords(@Param("now") LocalDateTime now);
    
    @Query("SELECT COUNT(br) FROM BorrowingRecord br WHERE br.user.id = :userId AND br.status = 1")
    long countActiveBorrowingsByUserId(@Param("userId") Long userId);
    
    @Query("SELECT br FROM BorrowingRecord br WHERE br.user.id = :userId AND br.book.id = :bookId AND br.status = 1")
    List<BorrowingRecord> findActiveBorrowingByUserAndBook(@Param("userId") Long userId, 
                                                          @Param("bookId") Long bookId);
    
    @Query("SELECT COUNT(br) FROM BorrowingRecord br WHERE br.status = 1")
    long countActiveBorrowings();
    
    @Query("SELECT COUNT(br) FROM BorrowingRecord br WHERE br.dueDate < :now AND br.status = 1")
    long countOverdueRecords(@Param("now") LocalDateTime now);
}
