package com.library.service;

import com.library.dto.BorrowingRecordDTO;
import com.library.entity.Book;
import com.library.entity.BorrowingRecord;
import com.library.entity.User;
import com.library.repository.BookRepository;
import com.library.repository.BorrowingRecordRepository;
import com.library.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Transactional
public class BorrowingService {
    
    private final BorrowingRecordRepository borrowingRecordRepository;
    private final BookRepository bookRepository;
    private final UserRepository userRepository;
    
    // 默认借阅天数
    private static final int DEFAULT_BORROW_DAYS = 30;
    // 最大借阅数量
    private static final int MAX_BORROW_COUNT = 5;
    // 每日罚金
    private static final BigDecimal DAILY_FINE = new BigDecimal("0.50");
    
    public BorrowingRecord borrowBook(Long userId, Long bookId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        Book book = bookRepository.findById(bookId)
                .orElseThrow(() -> new RuntimeException("图书不存在"));
        
        // 检查用户状态
        if (user.getStatus() != 1) {
            throw new RuntimeException("用户状态异常，无法借阅");
        }
        
        // 检查图书状态
        if (book.getStatus() != 1) {
            throw new RuntimeException("图书状态异常，无法借阅");
        }
        
        // 检查库存
        if (book.getAvailableCopies() <= 0) {
            throw new RuntimeException("图书库存不足");
        }
        
        // 检查用户当前借阅数量
        long currentBorrowCount = borrowingRecordRepository.countActiveBorrowingsByUserId(userId);
        if (currentBorrowCount >= MAX_BORROW_COUNT) {
            throw new RuntimeException("借阅数量已达上限");
        }
        
        // 检查是否已借阅同一本书
        List<BorrowingRecord> existingRecords = borrowingRecordRepository
                .findActiveBorrowingByUserAndBook(userId, bookId);
        if (!existingRecords.isEmpty()) {
            throw new RuntimeException("您已借阅此书，请先归还后再借阅");
        }
        
        // 创建借阅记录
        BorrowingRecord record = new BorrowingRecord();
        record.setUser(user);
        record.setBook(book);
        record.setBorrowDate(LocalDateTime.now());
        record.setDueDate(LocalDateTime.now().plusDays(DEFAULT_BORROW_DAYS));
        record.setStatus(1); // 借阅中
        record.setRenewCount(0);
        record.setFineAmount(BigDecimal.ZERO);
        record.setFinePaid(0);
        
        // 更新图书库存
        book.setAvailableCopies(book.getAvailableCopies() - 1);
        book.setBorrowCount(book.getBorrowCount() + 1);
        bookRepository.save(book);
        
        return borrowingRecordRepository.save(record);
    }
    
    public BorrowingRecord returnBook(Long recordId) {
        BorrowingRecord record = borrowingRecordRepository.findById(recordId)
                .orElseThrow(() -> new RuntimeException("借阅记录不存在"));
        
        if (record.getStatus() != 1) {
            throw new RuntimeException("该记录不是借阅中状态");
        }
        
        // 计算罚金
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(record.getDueDate())) {
            long overdueDays = java.time.Duration.between(record.getDueDate(), now).toDays();
            BigDecimal fine = DAILY_FINE.multiply(new BigDecimal(overdueDays));
            record.setFineAmount(fine);
        }
        
        // 更新记录状态
        record.setReturnDate(now);
        record.setStatus(2); // 已归还
        
        // 更新图书库存
        Book book = record.getBook();
        book.setAvailableCopies(book.getAvailableCopies() + 1);
        bookRepository.save(book);
        
        return borrowingRecordRepository.save(record);
    }
    
    public BorrowingRecord renewBook(Long recordId) {
        BorrowingRecord record = borrowingRecordRepository.findById(recordId)
                .orElseThrow(() -> new RuntimeException("借阅记录不存在"));
        
        if (record.getStatus() != 1) {
            throw new RuntimeException("该记录不是借阅中状态");
        }
        
        // 检查续借次数
        if (record.getRenewCount() >= 2) {
            throw new RuntimeException("续借次数已达上限");
        }
        
        // 检查是否逾期
        if (LocalDateTime.now().isAfter(record.getDueDate())) {
            throw new RuntimeException("图书已逾期，无法续借");
        }
        
        // 续借
        record.setDueDate(record.getDueDate().plusDays(DEFAULT_BORROW_DAYS));
        record.setRenewCount(record.getRenewCount() + 1);
        
        return borrowingRecordRepository.save(record);
    }
    
    public Page<BorrowingRecordDTO> findUserBorrowingRecords(Long userId, Pageable pageable) {
        return borrowingRecordRepository.findByUserId(userId, pageable)
                .map(this::convertToDTO);
    }
    
    public Page<BorrowingRecordDTO> findUserActiveBorrowings(Long userId, Pageable pageable) {
        return borrowingRecordRepository.findByUserIdAndStatus(userId, 1, pageable)
                .map(this::convertToDTO);
    }
    
    public Page<BorrowingRecordDTO> findAllBorrowingRecords(Pageable pageable) {
        return borrowingRecordRepository.findAll(pageable).map(this::convertToDTO);
    }
    
    public Page<BorrowingRecordDTO> findActiveBorrowings(Pageable pageable) {
        return borrowingRecordRepository.findActiveBorrowings(pageable)
                .map(this::convertToDTO);
    }
    
    public List<BorrowingRecord> findOverdueRecords() {
        return borrowingRecordRepository.findOverdueRecords(LocalDateTime.now());
    }
    
    public BorrowingRecordDTO convertToDTO(BorrowingRecord record) {
        BorrowingRecordDTO dto = new BorrowingRecordDTO();
        dto.setId(record.getId());
        dto.setUserId(record.getUser().getId());
        dto.setUsername(record.getUser().getUsername());
        dto.setUserRealName(record.getUser().getRealName());
        dto.setBookId(record.getBook().getId());
        dto.setBookTitle(record.getBook().getTitle());
        dto.setBookAuthor(record.getBook().getAuthor());
        dto.setBookIsbn(record.getBook().getIsbn());
        dto.setBorrowDate(record.getBorrowDate());
        dto.setDueDate(record.getDueDate());
        dto.setReturnDate(record.getReturnDate());
        dto.setRenewCount(record.getRenewCount());
        dto.setStatus(record.getStatus());
        dto.setFineAmount(record.getFineAmount());
        dto.setFinePaid(record.getFinePaid());
        dto.setRemark(record.getRemark());
        dto.setCreatedAt(record.getCreatedAt());
        dto.setUpdatedAt(record.getUpdatedAt());
        return dto;
    }
}
