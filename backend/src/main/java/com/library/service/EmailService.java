package com.library.service;

import com.library.entity.EmailNotification;
import com.library.entity.User;
import com.library.repository.EmailNotificationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.mail.internet.MimeMessage;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class EmailService {
    
    private final JavaMailSender mailSender;
    private final EmailNotificationRepository emailNotificationRepository;
    private final TemplateEngine templateEngine;
    
    @Value("${spring.mail.username}")
    private String fromEmail;
    
    @Value("${spring.application.name:智慧图书馆}")
    private String systemName;
    
    /**
     * 创建邮件通知记录
     */
    public EmailNotification createEmailNotification(User user, String subject, String content, String type) {
        EmailNotification notification = new EmailNotification();
        notification.setUser(user);
        notification.setEmail(user.getEmail());
        notification.setSubject(subject);
        notification.setContent(content);
        notification.setType(type);
        notification.setStatus(0); // 待发送
        
        return emailNotificationRepository.save(notification);
    }
    
    /**
     * 异步发送邮件
     */
    @Async
    public void sendEmailAsync(Long notificationId) {
        EmailNotification notification = emailNotificationRepository.findById(notificationId)
                .orElse(null);
        
        if (notification == null) {
            log.error("邮件通知记录不存在: {}", notificationId);
            return;
        }
        
        sendEmail(notification);
    }
    
    /**
     * 发送邮件
     */
    public void sendEmail(EmailNotification notification) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            
            helper.setFrom(fromEmail, systemName);
            helper.setTo(notification.getEmail());
            helper.setSubject(notification.getSubject());
            helper.setText(notification.getContent(), true);
            
            mailSender.send(message);
            
            // 更新发送状态
            notification.setStatus(1); // 已发送
            notification.setSentAt(LocalDateTime.now());
            emailNotificationRepository.save(notification);
            
            log.info("邮件发送成功: {} -> {}", notification.getSubject(), notification.getEmail());
            
        } catch (Exception e) {
            log.error("邮件发送失败: {} -> {}, 错误: {}", 
                    notification.getSubject(), notification.getEmail(), e.getMessage());
            
            // 更新失败状态
            notification.setStatus(2); // 发送失败
            notification.setErrorMessage(e.getMessage());
            notification.setRetryCount(notification.getRetryCount() + 1);
            emailNotificationRepository.save(notification);
        }
    }
    
    /**
     * 发送预约可借阅通知
     */
    public void sendReservationAvailableNotification(User user, String bookTitle, int holdDays) {
        String subject = "【" + systemName + "】您预约的图书可以借阅了";
        
        Context context = new Context();
        context.setVariable("userName", user.getRealName() != null ? user.getRealName() : user.getUsername());
        context.setVariable("bookTitle", bookTitle);
        context.setVariable("holdDays", holdDays);
        context.setVariable("systemName", systemName);
        
        String content = generateEmailContent("reservation-available", context);
        
        EmailNotification notification = createEmailNotification(user, subject, content, "RESERVATION_AVAILABLE");
        sendEmailAsync(notification.getId());
    }
    
    /**
     * 发送逾期提醒通知
     */
    public void sendOverdueReminderNotification(User user, String bookTitle, LocalDateTime dueDate, int overdueDays) {
        String subject = "【" + systemName + "】图书逾期提醒";
        
        Context context = new Context();
        context.setVariable("userName", user.getRealName() != null ? user.getRealName() : user.getUsername());
        context.setVariable("bookTitle", bookTitle);
        context.setVariable("dueDate", dueDate);
        context.setVariable("overdueDays", overdueDays);
        context.setVariable("systemName", systemName);
        
        String content = generateEmailContent("overdue-reminder", context);
        
        EmailNotification notification = createEmailNotification(user, subject, content, "OVERDUE_REMINDER");
        sendEmailAsync(notification.getId());
    }
    
    /**
     * 发送归还提醒通知
     */
    public void sendReturnReminderNotification(User user, String bookTitle, LocalDateTime dueDate, int reminderDays) {
        String subject = "【" + systemName + "】图书归还提醒";
        
        Context context = new Context();
        context.setVariable("userName", user.getRealName() != null ? user.getRealName() : user.getUsername());
        context.setVariable("bookTitle", bookTitle);
        context.setVariable("dueDate", dueDate);
        context.setVariable("reminderDays", reminderDays);
        context.setVariable("systemName", systemName);
        
        String content = generateEmailContent("return-reminder", context);
        
        EmailNotification notification = createEmailNotification(user, subject, content, "RETURN_REMINDER");
        sendEmailAsync(notification.getId());
    }
    
    /**
     * 生成邮件内容
     */
    private String generateEmailContent(String templateName, Context context) {
        try {
            return templateEngine.process("email/" + templateName, context);
        } catch (Exception e) {
            log.error("邮件模板处理失败: {}", templateName, e);
            return generateFallbackContent(context);
        }
    }
    
    /**
     * 生成备用邮件内容
     */
    private String generateFallbackContent(Context context) {
        StringBuilder content = new StringBuilder();
        content.append("<html><body>");
        content.append("<h2>").append(systemName).append("</h2>");
        content.append("<p>尊敬的用户，您好！</p>");
        content.append("<p>这是来自").append(systemName).append("的通知。</p>");
        content.append("<p>如有疑问，请联系图书馆管理员。</p>");
        content.append("</body></html>");
        return content.toString();
    }
    
    /**
     * 处理待发送的邮件
     */
    public void processPendingEmails() {
        List<EmailNotification> pendingNotifications = emailNotificationRepository.findPendingNotifications();
        
        for (EmailNotification notification : pendingNotifications) {
            sendEmail(notification);
        }
        
        log.info("处理了 {} 封待发送邮件", pendingNotifications.size());
    }
}
