package com.library.service;

import com.library.dto.BookReservationDTO;
import com.library.entity.Book;
import com.library.entity.BookReservation;
import com.library.entity.User;
import com.library.repository.BookRepository;
import com.library.repository.BookReservationRepository;
import com.library.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class ReservationService {
    
    private final BookReservationRepository reservationRepository;
    private final BookRepository bookRepository;
    private final UserRepository userRepository;
    private final EmailService emailService;
    
    // 配置常量
    private static final int MAX_RESERVATIONS_PER_USER = 3;
    private static final int RESERVATION_HOLD_DAYS = 3;
    private static final int RESERVATION_EXPIRY_HOURS = 24;
    
    /**
     * 预约图书
     */
    public BookReservation reserveBook(Long userId, Long bookId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        Book book = bookRepository.findById(bookId)
                .orElseThrow(() -> new RuntimeException("图书不存在"));
        
        // 检查用户状态
        if (user.getStatus() != 1) {
            throw new RuntimeException("用户状态异常，无法预约");
        }
        
        // 检查图书状态
        if (book.getStatus() != 1) {
            throw new RuntimeException("图书状态异常，无法预约");
        }
        
        // 检查是否有库存
        if (book.getAvailableCopies() > 0) {
            throw new RuntimeException("图书有库存，请直接借阅");
        }
        
        // 检查用户预约数量限制
        long currentReservations = reservationRepository.countActiveReservationsByUserId(userId);
        if (currentReservations >= MAX_RESERVATIONS_PER_USER) {
            throw new RuntimeException("预约数量已达上限（" + MAX_RESERVATIONS_PER_USER + "本）");
        }
        
        // 检查是否已预约同一本书
        Optional<BookReservation> existingReservation = reservationRepository
                .findActiveReservationByUserAndBook(userId, bookId);
        if (existingReservation.isPresent()) {
            throw new RuntimeException("您已预约此书，请勿重复预约");
        }
        
        // 计算队列位置
        Integer maxPosition = reservationRepository.findMaxQueuePositionByBookId(bookId);
        int queuePosition = (maxPosition != null ? maxPosition : 0) + 1;
        
        // 创建预约记录
        BookReservation reservation = new BookReservation();
        reservation.setUser(user);
        reservation.setBook(book);
        reservation.setReservationDate(LocalDateTime.now());
        reservation.setExpiryDate(LocalDateTime.now().plusDays(RESERVATION_HOLD_DAYS));
        reservation.setStatus(1); // 等待中
        reservation.setQueuePosition(queuePosition);
        reservation.setNotificationSent(0);
        
        return reservationRepository.save(reservation);
    }
    
    /**
     * 取消预约
     */
    public void cancelReservation(Long reservationId, Long userId) {
        BookReservation reservation = reservationRepository.findById(reservationId)
                .orElseThrow(() -> new RuntimeException("预约记录不存在"));
        
        if (!reservation.getUser().getId().equals(userId)) {
            throw new RuntimeException("无权取消此预约");
        }
        
        if (reservation.getStatus() != 1 && reservation.getStatus() != 2) {
            throw new RuntimeException("当前状态无法取消预约");
        }
        
        // 更新预约状态
        reservation.setStatus(5); // 已取消
        reservationRepository.save(reservation);
        
        // 如果是等待中的预约，需要更新后续预约的队列位置
        if (reservation.getStatus() == 1) {
            reservationRepository.updateQueuePositionsAfterCancellation(
                    reservation.getBook().getId(), 
                    reservation.getQueuePosition()
            );
        }
        
        log.info("用户 {} 取消了图书 {} 的预约", userId, reservation.getBook().getTitle());
    }
    
    /**
     * 处理图书归还后的预约队列
     */
    public void processReservationQueueAfterReturn(Long bookId) {
        List<BookReservation> waitingReservations = reservationRepository
                .findWaitingReservationsByBookId(bookId);
        
        if (!waitingReservations.isEmpty()) {
            // 通知队列中的第一个用户
            BookReservation firstReservation = waitingReservations.get(0);
            firstReservation.setStatus(2); // 可借阅
            firstReservation.setExpiryDate(LocalDateTime.now().plusHours(RESERVATION_EXPIRY_HOURS));
            reservationRepository.save(firstReservation);
            
            // 发送邮件通知
            emailService.sendReservationAvailableNotification(
                    firstReservation.getUser(),
                    firstReservation.getBook().getTitle(),
                    RESERVATION_EXPIRY_HOURS
            );
            
            log.info("通知用户 {} 预约的图书 {} 可以借阅", 
                    firstReservation.getUser().getUsername(), 
                    firstReservation.getBook().getTitle());
        }
    }
    
    /**
     * 处理预约过期
     */
    public void processExpiredReservations() {
        List<BookReservation> expiredReservations = reservationRepository
                .findExpiredAvailableReservations(LocalDateTime.now());
        
        for (BookReservation reservation : expiredReservations) {
            reservation.setStatus(4); // 已过期
            reservationRepository.save(reservation);
            
            // 处理下一个预约
            processReservationQueueAfterReturn(reservation.getBook().getId());
            
            log.info("预约过期：用户 {} 的图书 {} 预约已过期", 
                    reservation.getUser().getUsername(), 
                    reservation.getBook().getTitle());
        }
    }
    
    /**
     * 获取用户的预约记录
     */
    public Page<BookReservationDTO> getUserReservations(Long userId, Pageable pageable) {
        return reservationRepository.findByUserId(userId, pageable)
                .map(this::convertToDTO);
    }
    
    /**
     * 获取用户的活跃预约
     */
    public Page<BookReservationDTO> getUserActiveReservations(Long userId, Pageable pageable) {
        return reservationRepository.findActiveReservationsByUserId(userId, pageable)
                .map(this::convertToDTO);
    }
    
    /**
     * 获取所有活跃预约（管理员）
     */
    public Page<BookReservationDTO> getAllActiveReservations(Pageable pageable) {
        return reservationRepository.findActiveReservations(pageable)
                .map(this::convertToDTO);
    }
    
    /**
     * 转换为DTO
     */
    public BookReservationDTO convertToDTO(BookReservation reservation) {
        BookReservationDTO dto = new BookReservationDTO();
        dto.setId(reservation.getId());
        dto.setUserId(reservation.getUser().getId());
        dto.setUsername(reservation.getUser().getUsername());
        dto.setUserRealName(reservation.getUser().getRealName());
        dto.setBookId(reservation.getBook().getId());
        dto.setBookTitle(reservation.getBook().getTitle());
        dto.setBookAuthor(reservation.getBook().getAuthor());
        dto.setBookIsbn(reservation.getBook().getIsbn());
        dto.setReservationDate(reservation.getReservationDate());
        dto.setExpiryDate(reservation.getExpiryDate());
        dto.setStatus(reservation.getStatus());
        dto.setQueuePosition(reservation.getQueuePosition());
        dto.setNotificationSent(reservation.getNotificationSent());
        dto.setRemark(reservation.getRemark());
        dto.setCreatedAt(reservation.getCreatedAt());
        dto.setUpdatedAt(reservation.getUpdatedAt());
        return dto;
    }
    
    /**
     * 管理员取消预约
     */
    public void adminCancelReservation(Long reservationId) {
        BookReservation reservation = reservationRepository.findById(reservationId)
                .orElseThrow(() -> new RuntimeException("预约记录不存在"));
        
        reservation.setStatus(5); // 已取消
        reservationRepository.save(reservation);
        
        // 更新队列位置
        if (reservation.getQueuePosition() != null) {
            reservationRepository.updateQueuePositionsAfterCancellation(
                    reservation.getBook().getId(), 
                    reservation.getQueuePosition()
            );
        }
        
        log.info("管理员取消了预约：用户 {} 的图书 {}", 
                reservation.getUser().getUsername(), 
                reservation.getBook().getTitle());
    }
}
