package com.library.service;

import com.library.entity.BorrowingRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScheduledTaskService {
    
    private final ReservationService reservationService;
    private final EmailService emailService;
    private final BorrowingService borrowingService;
    
    /**
     * 每小时处理预约过期
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void processExpiredReservations() {
        try {
            log.info("开始处理预约过期任务");
            reservationService.processExpiredReservations();
            log.info("预约过期任务处理完成");
        } catch (Exception e) {
            log.error("处理预约过期任务失败", e);
        }
    }
    
    /**
     * 每30分钟处理待发送邮件
     */
    @Scheduled(fixedRate = 1800000) // 30分钟
    public void processPendingEmails() {
        try {
            log.info("开始处理待发送邮件");
            emailService.processPendingEmails();
            log.info("待发送邮件处理完成");
        } catch (Exception e) {
            log.error("处理待发送邮件失败", e);
        }
    }
    
    /**
     * 每天早上8点发送逾期提醒
     */
    @Scheduled(cron = "0 0 8 * * ?")
    public void sendOverdueReminders() {
        try {
            log.info("开始发送逾期提醒");
            List<BorrowingRecord> overdueRecords = borrowingService.findOverdueRecords();
            
            for (BorrowingRecord record : overdueRecords) {
                LocalDateTime now = LocalDateTime.now();
                long overdueDays = java.time.Duration.between(record.getDueDate(), now).toDays();
                
                emailService.sendOverdueReminderNotification(
                        record.getUser(),
                        record.getBook().getTitle(),
                        record.getDueDate(),
                        (int) overdueDays
                );
            }
            
            log.info("逾期提醒发送完成，共发送 {} 条", overdueRecords.size());
        } catch (Exception e) {
            log.error("发送逾期提醒失败", e);
        }
    }
    
    /**
     * 每天早上9点发送归还提醒（提前3天）
     */
    @Scheduled(cron = "0 0 9 * * ?")
    public void sendReturnReminders() {
        try {
            log.info("开始发送归还提醒");
            // 这里需要实现查找即将到期的借阅记录的逻辑
            // 暂时省略具体实现
            log.info("归还提醒发送完成");
        } catch (Exception e) {
            log.error("发送归还提醒失败", e);
        }
    }
}
