package com.library.service;

import com.library.entity.Category;
import com.library.repository.CategoryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Transactional
public class CategoryService {
    
    private final CategoryRepository categoryRepository;
    
    public List<Category> findAllActiveCategories() {
        return categoryRepository.findActiveCategories();
    }
    
    public List<Category> findRootCategories() {
        return categoryRepository.findRootCategories();
    }
    
    public List<Category> findByParentId(Long parentId) {
        return categoryRepository.findByParentId(parentId);
    }
    
    public Optional<Category> findById(Long id) {
        return categoryRepository.findById(id);
    }
    
    public Category createCategory(String name, String description, Long parentId, Integer sortOrder) {
        if (categoryRepository.existsByName(name)) {
            throw new RuntimeException("分类名称已存在");
        }
        
        Category category = new Category();
        category.setName(name);
        category.setDescription(description);
        category.setSortOrder(sortOrder != null ? sortOrder : 0);
        category.setStatus(1);
        
        if (parentId != null) {
            Category parent = categoryRepository.findById(parentId)
                    .orElseThrow(() -> new RuntimeException("父分类不存在"));
            category.setParent(parent);
        }
        
        return categoryRepository.save(category);
    }
    
    public Category updateCategory(Long id, String name, String description, Integer sortOrder) {
        Category category = categoryRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("分类不存在"));
        
        if (!category.getName().equals(name) && categoryRepository.existsByName(name)) {
            throw new RuntimeException("分类名称已存在");
        }
        
        category.setName(name);
        category.setDescription(description);
        if (sortOrder != null) {
            category.setSortOrder(sortOrder);
        }
        
        return categoryRepository.save(category);
    }
    
    public void deleteCategory(Long id) {
        Category category = categoryRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("分类不存在"));
        
        // 检查是否有子分类
        if (!category.getChildren().isEmpty()) {
            throw new RuntimeException("该分类下有子分类，无法删除");
        }
        
        // 检查是否有图书
        if (!category.getBooks().isEmpty()) {
            throw new RuntimeException("该分类下有图书，无法删除");
        }
        
        categoryRepository.delete(category);
    }
}
