package com.library.service;

import com.library.dto.LibraryStatsDTO;
import com.library.repository.BookRepository;
import com.library.repository.BorrowingRecordRepository;
import com.library.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class StatsService {
    
    private final UserRepository userRepository;
    private final BookRepository bookRepository;
    private final BorrowingRecordRepository borrowingRecordRepository;
    
    public LibraryStatsDTO getLibraryStats() {
        LibraryStatsDTO stats = new LibraryStatsDTO();
        
        // 用户统计
        stats.setTotalUsers(userRepository.count());
        stats.setActiveUsers(userRepository.findActiveUsers(org.springframework.data.domain.Pageable.unpaged()).getTotalElements());
        
        // 图书统计
        stats.setTotalBooks(bookRepository.countAvailableBooks());
        stats.setTotalCopies(bookRepository.countTotalCopies());
        stats.setAvailableCopies(bookRepository.countAvailableCopies());
        stats.setBorrowedCopies(stats.getTotalCopies() - stats.getAvailableCopies());
        
        // 借阅统计
        stats.setActiveBorrowings(borrowingRecordRepository.countActiveBorrowings());
        stats.setOverdueRecords(borrowingRecordRepository.countOverdueRecords(LocalDateTime.now()));
        stats.setTotalBorrowings(borrowingRecordRepository.count());
        stats.setReturnedBorrowings(stats.getTotalBorrowings() - stats.getActiveBorrowings());
        
        return stats;
    }
}
