package com.library.service;

import com.library.dto.BookDTO;
import com.library.dto.BookRequest;
import com.library.entity.Book;
import com.library.entity.Category;
import com.library.repository.BookRepository;
import com.library.repository.CategoryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Transactional
public class BookService {
    
    private final BookRepository bookRepository;
    private final CategoryRepository categoryRepository;
    
    public Book createBook(BookRequest bookRequest) {
        if (bookRequest.getIsbn() != null && bookRepository.existsByIsbn(bookRequest.getIsbn())) {
            throw new RuntimeException("ISBN已存在");
        }
        
        Book book = new Book();
        updateBookFromRequest(book, bookRequest);
        return bookRepository.save(book);
    }
    
    public Book updateBook(Long bookId, BookRequest bookRequest) {
        Book book = bookRepository.findById(bookId)
                .orElseThrow(() -> new RuntimeException("图书不存在"));
        
        // 检查ISBN是否被其他图书使用
        if (bookRequest.getIsbn() != null && !bookRequest.getIsbn().equals(book.getIsbn())) {
            if (bookRepository.existsByIsbn(bookRequest.getIsbn())) {
                throw new RuntimeException("ISBN已存在");
            }
        }
        
        updateBookFromRequest(book, bookRequest);
        return bookRepository.save(book);
    }
    
    private void updateBookFromRequest(Book book, BookRequest request) {
        book.setTitle(request.getTitle());
        book.setAuthor(request.getAuthor());
        book.setIsbn(request.getIsbn());
        book.setPublisher(request.getPublisher());
        book.setPublishDate(request.getPublishDate());
        book.setTotalCopies(request.getTotalCopies());
        book.setAvailableCopies(request.getAvailableCopies());
        book.setDescription(request.getDescription());
        book.setCoverUrl(request.getCoverUrl());
        book.setPages(request.getPages());
        book.setLanguage(request.getLanguage());
        book.setLocation(request.getLocation());
        
        if (request.getCategoryId() != null) {
            Category category = categoryRepository.findById(request.getCategoryId())
                    .orElseThrow(() -> new RuntimeException("分类不存在"));
            book.setCategory(category);
        }
    }
    
    public Optional<Book> findById(Long id) {
        return bookRepository.findById(id);
    }
    
    public Page<BookDTO> findAllBooks(Pageable pageable) {
        return bookRepository.findAll(pageable).map(this::convertToDTO);
    }
    
    public Page<BookDTO> findAvailableBooks(Pageable pageable) {
        return bookRepository.findAvailableBooks(pageable).map(this::convertToDTO);
    }
    
    public Page<BookDTO> findBorrowableBooks(Pageable pageable) {
        return bookRepository.findBorrowableBooks(pageable).map(this::convertToDTO);
    }
    
    public Page<BookDTO> searchBooks(String keyword, Pageable pageable) {
        return bookRepository.searchBooks(keyword, pageable).map(this::convertToDTO);
    }
    
    public Page<BookDTO> findBooksByCategory(Long categoryId, Pageable pageable) {
        return bookRepository.findByCategoryId(categoryId, pageable).map(this::convertToDTO);
    }
    
    public List<BookDTO> findPopularBooks(Pageable pageable) {
        return bookRepository.findPopularBooks(pageable).stream()
                .map(this::convertToDTO)
                .collect(java.util.stream.Collectors.toList());
    }
    
    public BookDTO convertToDTO(Book book) {
        BookDTO dto = new BookDTO();
        dto.setId(book.getId());
        dto.setTitle(book.getTitle());
        dto.setAuthor(book.getAuthor());
        dto.setIsbn(book.getIsbn());
        dto.setPublisher(book.getPublisher());
        dto.setPublishDate(book.getPublishDate());
        dto.setTotalCopies(book.getTotalCopies());
        dto.setAvailableCopies(book.getAvailableCopies());
        dto.setDescription(book.getDescription());
        dto.setCoverUrl(book.getCoverUrl());
        dto.setPages(book.getPages());
        dto.setLanguage(book.getLanguage());
        dto.setLocation(book.getLocation());
        dto.setStatus(book.getStatus());
        dto.setBorrowCount(book.getBorrowCount());
        dto.setCreatedAt(book.getCreatedAt());
        dto.setUpdatedAt(book.getUpdatedAt());
        
        if (book.getCategory() != null) {
            dto.setCategoryId(book.getCategory().getId());
            dto.setCategoryName(book.getCategory().getName());
        }
        
        return dto;
    }
    
    public void deleteBook(Long bookId) {
        Book book = bookRepository.findById(bookId)
                .orElseThrow(() -> new RuntimeException("图书不存在"));
        
        // 检查是否有未归还的借阅记录
        if (book.getAvailableCopies() < book.getTotalCopies()) {
            throw new RuntimeException("该图书有未归还的借阅记录，无法删除");
        }
        
        bookRepository.delete(book);
    }
    
    public Book updateBookStatus(Long bookId, Integer status) {
        Book book = bookRepository.findById(bookId)
                .orElseThrow(() -> new RuntimeException("图书不存在"));
        book.setStatus(status);
        return bookRepository.save(book);
    }
}
