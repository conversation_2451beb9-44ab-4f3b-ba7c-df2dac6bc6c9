package com.library.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "books")
public class Book extends BaseEntity {
    
    @NotBlank
    @Size(max = 200)
    private String title;
    
    @NotBlank
    @Size(max = 100)
    private String author;
    
    @Size(max = 20)
    @Column(unique = true)
    private String isbn;
    
    @Size(max = 100)
    private String publisher;
    
    @Column(name = "publish_date")
    private LocalDate publishDate;
    
    @NotNull
    @Column(name = "total_copies")
    private Integer totalCopies = 1;
    
    @NotNull
    @Column(name = "available_copies")
    private Integer availableCopies = 1;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id")
    private Category category;
    
    @Lob
    private String description;
    
    @Size(max = 500)
    @Column(name = "cover_url")
    private String coverUrl;
    
    private Integer pages;
    
    @Size(max = 20)
    private String language = "zh-CN";
    
    @Size(max = 100)
    private String location; // 书架位置
    
    @Column(columnDefinition = "TINYINT DEFAULT 1")
    private Integer status = 1; // 1-可借，0-停借
    
    @Column(name = "borrow_count")
    private Integer borrowCount = 0;
    
    @OneToMany(mappedBy = "book", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<BorrowingRecord> borrowingRecords = new HashSet<>();
}
