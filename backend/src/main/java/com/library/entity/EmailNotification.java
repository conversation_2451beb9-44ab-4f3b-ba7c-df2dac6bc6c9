package com.library.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "email_notifications")
public class EmailNotification extends BaseEntity {
    
    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;
    
    @NotBlank
    @Column(length = 100)
    private String email;
    
    @NotBlank
    @Column(length = 200)
    private String subject;
    
    @NotBlank
    @Lob
    private String content;
    
    @NotBlank
    @Column(length = 50)
    private String type; // RESERVATION_AVAILABLE, OVERDUE_REMINDER, RETURN_REMINDER等
    
    @Column(columnDefinition = "TINYINT DEFAULT 0")
    private Integer status = 0; // 0-待发送，1-已发送，2-发送失败
    
    @Column(name = "sent_at")
    private LocalDateTime sentAt;
    
    @Lob
    @Column(name = "error_message")
    private String errorMessage;
    
    @Column(name = "retry_count")
    private Integer retryCount = 0;
    
    public String getStatusText() {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "待发送";
            case 1: return "已发送";
            case 2: return "发送失败";
            default: return "未知";
        }
    }
}
