package com.library.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "book_reservations")
public class BookReservation extends BaseEntity {
    
    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;
    
    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "book_id")
    private Book book;
    
    @Column(name = "reservation_date")
    private LocalDateTime reservationDate;
    
    @NotNull
    @Column(name = "expiry_date")
    private LocalDateTime expiryDate;
    
    @Column(columnDefinition = "TINYINT DEFAULT 1")
    private Integer status = 1; // 1-等待中，2-可借阅，3-已借阅，4-已过期，5-已取消
    
    @Column(name = "queue_position")
    private Integer queuePosition;
    
    @Column(name = "notification_sent", columnDefinition = "TINYINT DEFAULT 0")
    private Integer notificationSent = 0; // 0-未发送，1-已发送
    
    @Lob
    private String remark;
    
    @PrePersist
    protected void onCreate() {
        if (reservationDate == null) {
            reservationDate = LocalDateTime.now();
        }
    }
    
    public String getStatusText() {
        if (status == null) return "未知";
        switch (status) {
            case 1: return "等待中";
            case 2: return "可借阅";
            case 3: return "已借阅";
            case 4: return "已过期";
            case 5: return "已取消";
            default: return "未知";
        }
    }
}
