package com.library.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class BookReservationDTO {
    private Long id;
    private Long userId;
    private String username;
    private String userRealName;
    private Long bookId;
    private String bookTitle;
    private String bookAuthor;
    private String bookIsbn;
    private LocalDateTime reservationDate;
    private LocalDateTime expiryDate;
    private Integer status;
    private String statusText;
    private Integer queuePosition;
    private Integer notificationSent;
    private String remark;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    public String getStatusText() {
        if (status == null) return "未知";
        switch (status) {
            case 1: return "等待中";
            case 2: return "可借阅";
            case 3: return "已借阅";
            case 4: return "已过期";
            case 5: return "已取消";
            default: return "未知";
        }
    }
}
