package com.library.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;

@Data
public class BookRequest {
    
    @NotBlank(message = "书名不能为空")
    @Size(max = 200, message = "书名长度不能超过200个字符")
    private String title;
    
    @NotBlank(message = "作者不能为空")
    @Size(max = 100, message = "作者长度不能超过100个字符")
    private String author;
    
    @Size(max = 20, message = "ISBN长度不能超过20个字符")
    private String isbn;
    
    @Size(max = 100, message = "出版社长度不能超过100个字符")
    private String publisher;
    
    private LocalDate publishDate;
    
    @NotNull(message = "总册数不能为空")
    @Min(value = 1, message = "总册数必须大于0")
    private Integer totalCopies;
    
    @NotNull(message = "可借册数不能为空")
    @Min(value = 0, message = "可借册数不能小于0")
    private Integer availableCopies;
    
    private Long categoryId;
    
    private String description;
    
    @Size(max = 500, message = "封面URL长度不能超过500个字符")
    private String coverUrl;
    
    @Min(value = 1, message = "页数必须大于0")
    private Integer pages;
    
    @Size(max = 20, message = "语言长度不能超过20个字符")
    private String language = "zh-CN";
    
    @Size(max = 100, message = "位置长度不能超过100个字符")
    private String location;
}
