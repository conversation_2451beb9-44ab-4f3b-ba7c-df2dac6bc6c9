package com.library.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class BorrowingRecordDTO {
    private Long id;
    private Long userId;
    private String username;
    private String userRealName;
    private Long bookId;
    private String bookTitle;
    private String bookAuthor;
    private String bookIsbn;
    private LocalDateTime borrowDate;
    private LocalDateTime dueDate;
    private LocalDateTime returnDate;
    private Integer renewCount;
    private Integer status; // 1-借阅中，2-已归还，3-逾期，4-丢失
    private String statusText;
    private BigDecimal fineAmount;
    private Integer finePaid;
    private String remark;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    public String getStatusText() {
        if (status == null) return "未知";
        switch (status) {
            case 1: return "借阅中";
            case 2: return "已归还";
            case 3: return "逾期";
            case 4: return "丢失";
            default: return "未知";
        }
    }
}
