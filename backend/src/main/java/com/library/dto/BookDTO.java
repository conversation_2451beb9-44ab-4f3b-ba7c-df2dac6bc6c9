package com.library.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class BookDTO {
    private Long id;
    private String title;
    private String author;
    private String isbn;
    private String publisher;
    private LocalDate publishDate;
    private Integer totalCopies;
    private Integer availableCopies;
    private String categoryName;
    private Long categoryId;
    private String description;
    private String coverUrl;
    private Integer pages;
    private String language;
    private String location;
    private Integer status;
    private Integer borrowCount;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
