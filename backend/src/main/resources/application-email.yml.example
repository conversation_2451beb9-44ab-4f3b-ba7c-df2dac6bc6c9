# 邮件服务配置示例
# 复制此文件为 application-email.yml 并修改相应配置

spring:
  mail:
    # QQ邮箱配置
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-authorization-code  # QQ邮箱授权码，不是登录密码
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
    default-encoding: UTF-8

# 其他邮箱服务商配置示例：

# 163邮箱
# spring:
#   mail:
#     host: smtp.163.com
#     port: 25
#     username: <EMAIL>
#     password: your-authorization-code

# Gmail
# spring:
#   mail:
#     host: smtp.gmail.com
#     port: 587
#     username: <EMAIL>
#     password: your-app-password

# 企业邮箱
# spring:
#   mail:
#     host: smtp.exmail.qq.com
#     port: 587
#     username: <EMAIL>
#     password: your-password

# 邮件发送配置
email:
  enabled: true
  from-name: 智慧图书馆
  retry-max-count: 3
  batch-size: 50
