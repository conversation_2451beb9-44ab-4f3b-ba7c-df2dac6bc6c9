<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预约图书可借阅通知</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background: #f9f9f9;
            padding: 30px;
            border-radius: 0 0 8px 8px;
        }
        .book-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        .highlight {
            color: #667eea;
            font-weight: bold;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📚 预约图书可借阅通知</h1>
        <p th:text="${systemName}">智慧图书馆</p>
    </div>
    
    <div class="content">
        <p>尊敬的 <span class="highlight" th:text="${userName}">用户</span>，您好！</p>
        
        <p>恭喜您！您预约的图书现在可以借阅了：</p>
        
        <div class="book-info">
            <h3>📖 <span th:text="${bookTitle}">图书标题</span></h3>
            <p>您在预约队列中的等待已经结束，现在可以前往图书馆借阅这本书了。</p>
        </div>
        
        <div class="warning">
            <strong>⚠️ 重要提醒：</strong>
            <p>请在 <span class="highlight" th:text="${holdDays}">24</span> 小时内前往图书馆借阅，否则预约将自动过期，图书将分配给队列中的下一位读者。</p>
        </div>
        
        <p>借阅地点：图书馆服务台</p>
        <p>开放时间：8:00-22:00</p>
        
        <p>感谢您使用我们的图书预约服务！</p>
    </div>
    
    <div class="footer">
        <p th:text="${systemName}">智慧图书馆</p>
        <p>此邮件由系统自动发送，请勿回复</p>
    </div>
</body>
</html>
