server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: library-management-system
  
  datasource:
    url: ********************************************************************************************************************
    username: root
    password: hou153632001
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
    
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai

  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-email-password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
    default-encoding: UTF-8

# JWT配置
jwt:
  secret: mySecretKey123456789012345678901234567890
  expiration: 86400000 # 24小时

# 日志配置
logging:
  level:
    com.library: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
