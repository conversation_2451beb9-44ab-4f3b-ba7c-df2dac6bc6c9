#!/bin/bash

echo "=== 图书馆管理系统启动脚本 ==="

# 检查Java环境
echo "检查Java环境..."
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请先安装Java 11或更高版本"
    exit 1
fi

# 检查Node.js环境
echo "检查Node.js环境..."
if ! command -v node &> /dev/null; then
    echo "错误: 未找到Node.js环境，请先安装Node.js"
    exit 1
fi

# 检查MySQL环境
echo "检查MySQL环境..."
if ! command -v mysql &> /dev/null; then
    echo "警告: 未找到MySQL命令行工具，请确保MySQL服务正在运行"
fi

echo "=== 初始化数据库 ==="
echo "请确保MySQL服务正在运行，并且已创建数据库'library'"
echo "如需初始化数据库，请运行: mysql -u root -p < database/init.sql"

echo "=== 启动后端服务 ==="
cd backend
echo "正在启动SpringBoot后端..."
mvn spring-boot:run &
BACKEND_PID=$!
echo "后端服务PID: $BACKEND_PID"

# 等待后端启动
echo "等待后端服务启动..."
sleep 30

echo "=== 安装前端依赖 ==="
cd ../frontend
if [ ! -d "node_modules" ]; then
    echo "正在安装前端依赖..."
    npm install
fi

echo "=== 启动前端服务 ==="
echo "正在启动React前端..."
npm start &
FRONTEND_PID=$!
echo "前端服务PID: $FRONTEND_PID"

echo "=== 服务启动完成 ==="
echo "后端服务: http://localhost:8080/api"
echo "前端服务: http://localhost:3000"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap "echo '正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
